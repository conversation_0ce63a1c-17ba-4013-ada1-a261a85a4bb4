package com.scavenge.leaderboard;

import com.scavenge.ScavengePlugin;
import com.scavenge.level.LevelManager;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 玩家统计数据类
 */
public class PlayerStats {

    private UUID playerId;
    private String playerName;
    private int totalScavenges; // 总搜刮次数
    private int chestsCompleted; // 完成的搜刮箱数量
    private int rareItemsFound; // 发现的稀有物品数量
    private int questsCompleted; // 完成的任务数量
    private long totalPlayTime; // 总游戏时间（毫秒）
    private long lastActive; // 最后活跃时间
    private Map<String, Integer> itemsFound; // 发现的物品统计
    private LevelManager levelManager; // 等级管理器

    public PlayerStats(UUID playerId, String playerName) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.totalScavenges = 0;
        this.chestsCompleted = 0;
        this.rareItemsFound = 0;
        this.questsCompleted = 0;
        this.totalPlayTime = 0;
        this.lastActive = System.currentTimeMillis();
        this.itemsFound = new HashMap<>();
    }

    /**
     * 增加搜刮次数
     */
    public void addScavenge() {
        this.totalScavenges++;
        updateLastActive();
    }

    /**
     * 增加完成的搜刮箱数量
     */
    public void addChestCompleted() {
        this.chestsCompleted++;
        updateLastActive();
    }

    /**
     * 增加发现的稀有物品数量
     */
    public void addRareItem(String itemName) {
        this.rareItemsFound++;
        itemsFound.put(itemName, itemsFound.getOrDefault(itemName, 0) + 1);
        updateLastActive();
    }

    /**
     * 增加完成的任务数量
     */
    public void addQuestCompleted() {
        this.questsCompleted++;
        updateLastActive();
    }

    /**
     * 增加游戏时间
     */
    public void addPlayTime(long timeMs) {
        this.totalPlayTime += timeMs;
    }

    /**
     * 更新最后活跃时间
     */
    public void updateLastActive() {
        this.lastActive = System.currentTimeMillis();
    }

    /**
     * 获取搜刮效率（每小时搜刮次数）
     */
    public double getScavengeEfficiency() {
        if (totalPlayTime <= 0)
            return 0.0;
        double hours = totalPlayTime / (1000.0 * 60.0 * 60.0);
        return totalScavenges / hours;
    }

    /**
     * 获取稀有物品发现率
     */
    public double getRareItemRate() {
        if (totalScavenges <= 0)
            return 0.0;
        return (double) rareItemsFound / totalScavenges * 100.0;
    }

    /**
     * 获取任务完成率（假设有固定数量的任务）
     */
    public double getQuestCompletionRate(int totalQuests) {
        if (totalQuests <= 0)
            return 0.0;
        return (double) questsCompleted / totalQuests * 100.0;
    }

    /**
     * 计算综合评分
     */
    public double calculateScore() {
        // 综合评分算法：搜刮次数 * 2 + 完成箱子 * 3 + 稀有物品 * 5 + 任务完成 * 10
        return totalScavenges * 2.0 + chestsCompleted * 3.0 + rareItemsFound * 5.0 + questsCompleted * 10.0;
    }

    /**
     * 获取等级（基于总搜刮次数）
     */
    public int getLevel() {
        if (levelManager != null) {
            return levelManager.getLevel(totalScavenges);
        }
        // 如果没有等级管理器，使用默认等级
        return getDefaultLevel();
    }

    /**
     * 获取默认等级（备用方案）
     */
    private int getDefaultLevel() {
        if (totalScavenges >= 1000)
            return 10;
        if (totalScavenges >= 500)
            return 9;
        if (totalScavenges >= 300)
            return 8;
        if (totalScavenges >= 200)
            return 7;
        if (totalScavenges >= 100)
            return 6;
        if (totalScavenges >= 50)
            return 5;
        if (totalScavenges >= 25)
            return 4;
        if (totalScavenges >= 10)
            return 3;
        if (totalScavenges >= 5)
            return 2;
        return 1;
    }

    /**
     * 获取等级名称
     */
    public String getLevelName() {
        if (levelManager != null) {
            return levelManager.getLevelName(totalScavenges);
        }
        // 如果没有等级管理器，使用默认等级名称
        return getDefaultLevelName();
    }

    /**
     * 获取默认等级名称（备用方案）
     */
    private String getDefaultLevelName() {
        switch (getLevel()) {
            case 1:
                return "新手探索者";
            case 2:
                return "初级搜刮者";
            case 3:
                return "进阶搜刮者";
            case 4:
                return "活跃搜刮者";
            case 5:
                return "熟练搜刮者";
            case 6:
                return "搜刮高手";
            case 7:
                return "搜刮达人";
            case 8:
                return "搜刮专家";
            case 9:
                return "搜刮宗师";
            case 10:
                return "搜刮之神";
            default:
                return "未知等级";
        }
    }

    /**
     * 获取下一等级所需搜刮次数
     */
    public int getNextLevelRequirement() {
        if (levelManager != null) {
            return levelManager.getNextLevelRequirement(totalScavenges);
        }
        // 如果没有等级管理器，使用默认计算
        return getDefaultNextLevelRequirement();
    }

    /**
     * 获取默认下一等级要求（备用方案）
     */
    private int getDefaultNextLevelRequirement() {
        int[] requirements = { 0, 5, 10, 25, 50, 100, 200, 300, 500, 1000, Integer.MAX_VALUE };
        int currentLevel = getLevel();
        if (currentLevel >= 10)
            return 0; // 已达到最高等级
        return requirements[currentLevel] - totalScavenges;
    }

    // Getters and Setters
    public UUID getPlayerId() {
        return playerId;
    }

    public String getPlayerName() {
        return playerName;
    }

    public int getTotalScavenges() {
        return totalScavenges;
    }

    public int getChestsCompleted() {
        return chestsCompleted;
    }

    public int getRareItemsFound() {
        return rareItemsFound;
    }

    public int getQuestsCompleted() {
        return questsCompleted;
    }

    public long getTotalPlayTime() {
        return totalPlayTime;
    }

    public long getLastActive() {
        return lastActive;
    }

    public Map<String, Integer> getItemsFound() {
        return itemsFound;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public void setTotalScavenges(int totalScavenges) {
        this.totalScavenges = totalScavenges;
    }

    public void setChestsCompleted(int chestsCompleted) {
        this.chestsCompleted = chestsCompleted;
    }

    public void setRareItemsFound(int rareItemsFound) {
        this.rareItemsFound = rareItemsFound;
    }

    public void setQuestsCompleted(int questsCompleted) {
        this.questsCompleted = questsCompleted;
    }

    public void setTotalPlayTime(long totalPlayTime) {
        this.totalPlayTime = totalPlayTime;
    }

    public void setLastActive(long lastActive) {
        this.lastActive = lastActive;
    }

    public void setItemsFound(Map<String, Integer> itemsFound) {
        this.itemsFound = itemsFound;
    }

    /**
     * 设置等级管理器
     */
    public void setLevelManager(LevelManager levelManager) {
        this.levelManager = levelManager;
    }

    /**
     * 获取等级管理器
     */
    public LevelManager getLevelManager() {
        return levelManager;
    }
}
