package com.scavenge.gui;

import com.scavenge.ScavengePlugin;
import com.scavenge.quest.PlayerQuestProgress;
import com.scavenge.quest.ScavengeQuest;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务分类GUI界面
 */
public class QuestCategoryGUI implements Listener {

    private final ScavengePlugin plugin;
    private final Player player;
    private final ScavengeQuest.QuestType questType;
    private Inventory inventory;

    public QuestCategoryGUI(ScavengePlugin plugin, Player player, ScavengeQuest.QuestType questType) {
        this.plugin = plugin;
        this.player = player;
        this.questType = questType;
        createInventory();
        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 创建GUI界面
     */
    private void createInventory() {
        String typeName = getQuestTypeName(questType);
        inventory = Bukkit.createInventory(null, 54, "§6§l" + typeName);

        // 填充背景
        fillBackground();

        // 添加任务
        addQuests();

        // 添加导航按钮
        addNavigationButtons();
    }

    /**
     * 填充背景
     */
    private void fillBackground() {
        ItemStack background = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 7); // 灰色玻璃板
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);

        // 填充边框
        for (int i = 0; i < 9; i++) {
            inventory.setItem(i, background);
            inventory.setItem(45 + i, background);
        }
        for (int i = 0; i < 6; i++) {
            inventory.setItem(i * 9, background);
            inventory.setItem(i * 9 + 8, background);
        }
    }

    /**
     * 添加任务
     */
    private void addQuests() {
        List<ScavengeQuest> typeQuests = plugin.getQuestManager().getQuestsByType(player.getUniqueId(), questType);

        int slot = 10;

        // 添加该类型的所有任务
        for (ScavengeQuest quest : typeQuests) {
            if (slot >= 44)
                break; // 避免超出界面

            PlayerQuestProgress progress = plugin.getQuestManager().getPlayerProgress(player.getUniqueId(),
                    quest.getId());

            // 检查任务是否完成
            boolean isCompleted = progress.getCurrentProgress() >= quest.getTargetAmount();

            ItemStack questItem = createQuestItem(quest, progress, isCompleted);
            inventory.setItem(slot, questItem);

            slot++;
            if (slot % 9 == 8)
                slot += 2; // 跳过边框
        }
    }

    /**
     * 创建任务物品
     */
    private ItemStack createQuestItem(ScavengeQuest quest, PlayerQuestProgress progress, boolean completed) {
        Material material;
        String status;
        String displayName;

        if (completed) {
            if (progress.isClaimed()) {
                material = Material.EMERALD;
                status = "§a已领取";
                displayName = "§a已完成 §6" + quest.getName();
            } else {
                material = Material.DIAMOND;
                status = "§e可领取";
                displayName = "§e可领取 §6" + quest.getName();
            }
        } else {
            material = getQuestMaterial(quest.getType());
            status = "§7进行中";
            displayName = "§6" + quest.getName();
        }

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        meta.setDisplayName(displayName);

        List<String> lore = new ArrayList<>();
        lore.add("§7" + quest.getDescription());
        lore.add("");
        lore.add("§7类型: §f" + getQuestTypeName(quest.getType()));
        lore.add("§7目标: §f" + getQuestGoalName(quest.getGoal()));

        // 修复进度显示
        int displayProgress = progress.getCurrentProgress();
        if (completed && displayProgress < quest.getTargetAmount()) {
            displayProgress = quest.getTargetAmount();
        }

        lore.add("§7进度: §f" + displayProgress + "/" + quest.getTargetAmount());
        lore.add(progress.getProgressBar(quest.getTargetAmount(), 20));
        lore.add("§7状态: " + status);
        lore.add("");
        lore.add("§7剩余时间: §f" + quest.getRemainingTimeString());
        lore.add("");
        lore.add("§7奖励:");
        for (String reward : quest.getRewards()) {
            lore.add("§8- §f" + formatReward(reward));
        }

        if (completed && !progress.isClaimed()) {
            lore.add("");
            lore.add("§e点击领取奖励!");
        }

        meta.setLore(lore);
        item.setItemMeta(meta);

        return item;
    }

    /**
     * 获取任务类型对应的材质
     */
    private Material getQuestMaterial(ScavengeQuest.QuestType type) {
        switch (type) {
            case DAILY:
                return Material.WATCH;
            case WEEKLY:
                return Material.BOOK;
            case MONTHLY:
                return Material.BOOKSHELF;
            case SPECIAL:
                return Material.NETHER_STAR;
            default:
                return Material.PAPER;
        }
    }

    /**
     * 获取任务类型名称
     */
    private String getQuestTypeName(ScavengeQuest.QuestType type) {
        switch (type) {
            case DAILY:
                return "每日任务";
            case WEEKLY:
                return "每周任务";
            case MONTHLY:
                return "每月任务";
            case SPECIAL:
                return "特殊任务";
            default:
                return "未知";
        }
    }

    /**
     * 获取任务目标名称
     */
    private String getQuestGoalName(ScavengeQuest.QuestGoal goal) {
        switch (goal) {
            case SCAVENGE_COUNT:
                return "搜刮次数";
            case FIND_RARE_ITEMS:
                return "发现稀有物品";
            case COMPLETE_CHESTS:
                return "完成搜刮箱";
            case COLLECT_SPECIFIC:
                return "收集特定物品";
            case COLLECT_COMMAND_ITEM:
                return "收集指令物品";
            default:
                return "未知";
        }
    }

    /**
     * 格式化奖励显示
     */
    private String formatReward(String reward) {
        if (reward.startsWith("give {player} ")) {
            String itemPart = reward.substring("give {player} ".length());
            return parseGiveCommand(itemPart);
        } else if (reward.startsWith("eco give {player} ")) {
            String amount = reward.substring("eco give {player} ".length());
            return "§6" + amount + " 金币";
        } else if (reward.startsWith("broadcast ")) {
            // 提取广播内容并简化显示
            String broadcastContent = reward.substring("broadcast ".length());
            if (broadcastContent.contains("搜刮大师")) {
                return "§e全服广播：搜刮大师荣誉";
            } else if (broadcastContent.contains("探索专家")) {
                return "§e全服广播：探索专家荣誉";
            } else if (broadcastContent.contains("收藏家")) {
                return "§e全服广播：收藏家荣誉";
            } else if (broadcastContent.contains("宝石猎人")) {
                return "§e全服广播：宝石猎人荣誉";
            } else if (broadcastContent.contains("寻宝专家")) {
                return "§e全服广播：寻宝专家荣誉";
            } else if (broadcastContent.contains("传奇搜刮者")) {
                return "§c全服广播：传奇搜刮者荣誉";
            } else if (broadcastContent.contains("终极收集者")) {
                return "§d全服广播：终极收集者荣誉";
            } else if (broadcastContent.contains("搜刮宗师")) {
                return "§6全服广播：搜刮宗师荣誉";
            } else {
                return "§e全服广播荣誉";
            }
        } else if (reward.startsWith("effect {player} ")) {
            return "§d特殊效果";
        } else if (reward.startsWith("tell {player} ")) {
            return "§a特殊消息";
        } else {
            return "§7特殊奖励";
        }
    }

    /**
     * 解析give指令的物品部分
     */
    private String parseGiveCommand(String itemPart) {
        String[] parts = itemPart.split(" ");
        if (parts.length >= 2) {
            String material = parts[0];
            String amount = parts[1];
            String friendlyName = getFriendlyMaterialName(material);
            return "§f" + friendlyName + " §7x" + amount;
        }
        return "§7未知物品";
    }

    /**
     * 获取友好的材质名称
     */
    private String getFriendlyMaterialName(String material) {
        switch (material.toUpperCase()) {
            case "DIAMOND": return "钻石";
            case "EMERALD": return "绿宝石";
            case "GOLD_INGOT": return "金锭";
            case "IRON_INGOT": return "铁锭";
            case "BREAD": return "面包";
            case "EXPERIENCE_BOTTLE": return "经验瓶";
            case "DIAMOND_SWORD": return "钻石剑";
            case "GOLDEN_APPLE": return "金苹果";
            case "TRIPWIRE_HOOK": return "幸运钥匙";
            case "FEATHER": return "飞行羽毛";
            case "GOLD_NUGGET": return "财富符咒";
            case "POTION": return "治疗药水";
            case "EMERALD_BLOCK": return "绿宝石块";
            case "DIAMOND_BLOCK": return "钻石块";
            case "GOLD_BLOCK": return "金块";
            case "ENDER_PEARL": return "末影珍珠";
            case "ENCHANTED_BOOK": return "附魔书";
            case "NETHER_STAR": return "下界之星";
            case "BEACON": return "信标";
            case "COAL": return "煤炭";
            case "COAL_BLOCK": return "煤炭块";
            case "STICK": return "木棒";
            case "BLAZE_ROD": return "烈焰棒";
            case "PAPER": return "纸";
            case "SULPHUR": return "火药";
            case "WATCH": return "时钟";
            case "BOOK": return "书";
            case "BOOKSHELF": return "书架";
            default: return formatMaterialName(material);
        }
    }

    /**
     * 格式化原始材质名称
     */
    private String formatMaterialName(String material) {
        String[] words = material.toLowerCase().split("_");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (result.length() > 0) result.append(" ");
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) result.append(word.substring(1));
            }
        }
        return result.toString();
    }

    /**
     * 添加导航按钮
     */
    private void addNavigationButtons() {
        // 返回按钮
        ItemStack backButton = new ItemStack(Material.ARROW);
        ItemMeta meta = backButton.getItemMeta();
        meta.setDisplayName("§e返回任务中心");
        List<String> lore = new ArrayList<>();
        lore.add("§7返回任务分类选择");
        lore.add("§e点击返回!");
        meta.setLore(lore);
        backButton.setItemMeta(meta);
        inventory.setItem(45, backButton);

        // 关闭按钮
        ItemStack closeButton = new ItemStack(Material.BARRIER);
        meta = closeButton.getItemMeta();
        meta.setDisplayName("§c关闭");
        closeButton.setItemMeta(meta);
        inventory.setItem(53, closeButton);
    }

    /**
     * 打开GUI
     */
    public void open() {
        player.openInventory(inventory);
    }

    /**
     * 处理点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getInventory().equals(inventory))
            return;

        event.setCancelled(true);

        if (!(event.getWhoClicked() instanceof Player))
            return;
        Player clicker = (Player) event.getWhoClicked();

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR)
            return;

        // 处理任务点击（钻石表示可领取）
        if (clickedItem.getType() == Material.DIAMOND) {
            handleQuestClaim(clicker, event.getSlot());
        }
        // 处理其他任务点击
        else if (clickedItem.getType() == Material.WATCH || clickedItem.getType() == Material.BOOK ||
                clickedItem.getType() == Material.BOOKSHELF || clickedItem.getType() == Material.NETHER_STAR ||
                clickedItem.getType() == Material.PAPER || clickedItem.getType() == Material.EMERALD) {
            handleQuestClick(clicker, event.getSlot());
        }
        // 处理返回按钮
        else if (event.getSlot() == 45) {
            clicker.closeInventory();
            QuestGUI questGUI = new QuestGUI(plugin, clicker);
            questGUI.open();
        }
        // 处理关闭按钮
        else if (event.getSlot() == 53) {
            clicker.closeInventory();
        }
    }

    /**
     * 处理任务点击
     */
    private void handleQuestClick(Player clicker, int slot) {
        List<ScavengeQuest> typeQuests = plugin.getQuestManager().getQuestsByType(clicker.getUniqueId(), questType);
        int questIndex = calculateQuestIndex(slot);

        if (questIndex >= 0 && questIndex < typeQuests.size()) {
            ScavengeQuest quest = typeQuests.get(questIndex);
            PlayerQuestProgress progress = plugin.getQuestManager().getPlayerProgress(clicker.getUniqueId(), quest.getId());

            if (progress.getCurrentProgress() >= quest.getTargetAmount() && !progress.isClaimed()) {
                if (plugin.getQuestManager().claimQuestReward(clicker.getUniqueId(), quest.getId())) {
                    createInventory();
                    clicker.openInventory(inventory);
                }
            }
        }
    }

    /**
     * 处理任务奖励领取
     */
    private void handleQuestClaim(Player clicker, int slot) {
        List<ScavengeQuest> typeQuests = plugin.getQuestManager().getQuestsByType(clicker.getUniqueId(), questType);
        int questIndex = calculateQuestIndex(slot);

        if (questIndex >= 0 && questIndex < typeQuests.size()) {
            ScavengeQuest quest = typeQuests.get(questIndex);
            PlayerQuestProgress progress = plugin.getQuestManager().getPlayerProgress(clicker.getUniqueId(), quest.getId());

            boolean isCompleted = progress.getCurrentProgress() >= quest.getTargetAmount();
            if (isCompleted && !progress.isClaimed()) {
                if (plugin.getQuestManager().claimQuestReward(clicker.getUniqueId(), quest.getId())) {
                    createInventory();
                    clicker.openInventory(inventory);
                }
            }
        }
    }

    /**
     * 根据slot计算任务索引
     */
    private int calculateQuestIndex(int slot) {
        int row = slot / 9;
        int col = slot % 9;
        if (row >= 1 && row <= 4 && col >= 1 && col <= 7) {
            return (row - 1) * 7 + (col - 1);
        }
        return -1;
    }
}
