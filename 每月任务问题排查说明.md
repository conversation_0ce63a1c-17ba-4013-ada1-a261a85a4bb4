# 每月任务显示问题排查说明

## 问题现象
每月任务分类界面显示为空，没有任务显示。

## 可能原因分析

### 1. 配置文件加载问题
- 系统优先从 `quests.yml` 配置文件加载任务
- 如果配置文件存在但格式有误，可能导致任务加载失败
- 如果配置文件不存在，系统会创建默认任务

### 2. 任务时间设置问题
- 任务可能因为时间设置错误而被标记为过期
- 任务的 `isActive()` 或 `isExpired()` 状态可能有问题

### 3. 任务类型匹配问题
- 配置文件中的任务类型可能与代码中的枚举不匹配
- 大小写敏感问题

## 排查步骤

### 步骤1: 检查配置文件
1. 确认 `quests.yml` 文件存在
2. 检查文件中是否有 `type: "MONTHLY"` 的任务
3. 确认配置文件格式正确

### 步骤2: 重新加载任务配置
使用新增的命令重新加载任务：
```
/scavenge reloadquests
```

### 步骤3: 查看服务器日志
检查服务器控制台输出，查看：
- 任务加载日志
- 任务类型匹配日志
- 任务状态检查日志

### 步骤4: 使用调试功能
新增的调试日志会显示：
- 总任务数量
- 每个任务的类型匹配情况
- 任务的活跃状态
- 任务的过期状态

## 新增功能

### 1. 任务重新加载命令
```
/scavenge reloadquests
```
- 清空当前任务缓存
- 重新从配置文件加载任务
- 显示加载的任务数量

### 2. 详细调试日志
在 `getQuestsByType()` 方法中添加了详细的调试信息：
- 显示查询的任务类型
- 显示总任务数
- 显示每个任务的匹配情况
- 显示最终找到的任务数量

### 3. 任务状态检查
系统会检查每个任务的：
- 类型匹配 (`quest.getType() == type`)
- 活跃状态 (`quest.isActive()`)
- 过期状态 (`!quest.isExpired()`)

## 配置文件检查清单

### 每月任务配置示例
```yaml
monthly_scavenge_100:
  name: "月度搜刮大师"
  description: "在一个月内完成100次搜刮"
  type: "MONTHLY"  # 确保类型正确
  goal: "SCAVENGE_COUNT"
  target-amount: 100
  rewards:
    - "give {player} diamond_block 5"
  console: true
  rare: true
```

### 关键检查点
1. `type: "MONTHLY"` - 类型必须是大写
2. `goal` - 目标类型必须正确
3. `target-amount` - 目标数量必须是数字
4. `rewards` - 奖励列表格式正确
5. `console: true` - 布尔值格式正确

## 测试步骤

### 1. 服务器端测试
1. 启动服务器
2. 执行 `/scavenge reloadquests`
3. 查看控制台日志输出
4. 确认任务加载数量

### 2. 客户端测试
1. 进入游戏
2. 执行 `/scavenge quest`
3. 点击"每月任务"按钮
4. 查看是否显示任务

### 3. 日志分析
查看控制台输出中的关键信息：
```
[INFO] 重新加载任务配置...
[INFO] 加载任务: 月度搜刮大师
[INFO] 任务配置重新加载完成，共加载 X 个任务
[INFO] === 获取任务类型: MONTHLY ===
[INFO] 总任务数: X
[INFO] 找到 MONTHLY 类型任务: X 个
```

## 常见问题解决

### 问题1: 配置文件格式错误
**症状**: 任务加载失败，控制台显示解析错误
**解决**: 检查YAML格式，确保缩进正确

### 问题2: 任务类型不匹配
**症状**: 任务加载成功但分类显示为空
**解决**: 确认 `type: "MONTHLY"` 使用大写

### 问题3: 任务被标记为过期
**症状**: 任务加载但不显示
**解决**: 检查任务时间设置，重新加载任务

### 问题4: 权限问题
**症状**: 无法执行重新加载命令
**解决**: 确保有 `scavenge.admin` 权限

## 预期结果

正常情况下应该看到：
1. 控制台显示任务加载成功
2. 每月任务界面显示8个任务
3. 任务包括：月度搜刮大师、月度探索专家等
4. 每个任务显示正确的进度和状态

如果问题仍然存在，请提供：
1. 服务器控制台的完整日志
2. `quests.yml` 文件内容
3. 执行 `/scavenge reloadquests` 后的输出
