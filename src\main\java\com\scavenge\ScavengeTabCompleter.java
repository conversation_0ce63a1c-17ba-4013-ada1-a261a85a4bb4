package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.stream.Collectors;

public class ScavengeTabCompleter implements TabCompleter {

    private final ScavengePlugin plugin;

    public ScavengeTabCompleter(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数：主命令
            List<String> subCommands = Arrays.asList("give", "place", "remove", "list", "reset", "reload", "quest",
                    "quests", "leaderboard", "lb", "top", "debug", "test", "world", "setspawn",
                    "zone", "evacuation", "evac", "crafting", "craft");

            // 根据权限过滤命令
            if (sender.hasPermission("scavenge.admin")) {
                completions.addAll(subCommands);
            }

            // 过滤匹配的命令
            return completions.stream()
                    .filter(s -> s.toLowerCase().startsWith(args[0].toLowerCase()))
                    .sorted()
                    .collect(Collectors.toList());
        }

        if (args.length == 2) {
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "give":
                    // 给予命令：补全在线玩家名
                    if (sender.hasPermission("scavenge.admin")) {
                        return getOnlinePlayerNames(args[1]);
                    }
                    break;

                case "reset":
                    // 重置命令：all 或 target
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> resetOptions = Arrays.asList("all", "target");
                        return resetOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "world":
                    // 世界管理命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> worldOptions = Arrays.asList("add", "remove", "list", "enable", "disable");
                        return worldOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "evacuation":
                case "evac":
                case "zone":
                    // 撤离区域管理命令（evacuation重定向到zone）
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> zoneOptions = Arrays.asList("tool", "create", "remove", "list", "reload");
                        return zoneOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "debug":
                    // 调试命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> debugOptions = Arrays.asList("quests", "progress", "stats");
                        return debugOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "test":
                    // 测试命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> testOptions = Arrays.asList("quest", "stats");
                        return testOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "crafting":
                case "craft":
                    // 合成系统命令
                    List<String> craftingOptions = Arrays.asList("info", "recipes", "stats");
                    if (sender.hasPermission("scavenge.admin")) {
                        craftingOptions = Arrays.asList("info", "recipes", "stats", "reload", "save",
                            "enable", "disable", "test", "debug", "verify", "check");
                    }
                    return craftingOptions.stream()
                            .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                            .collect(Collectors.toList());

                default:
                    break;
            }
        }

        if (args.length == 3) {
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "give":
                    // 给予命令的第三个参数：数量（可选）
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> amounts = Arrays.asList("1", "2", "3", "4", "5", "10", "16", "32", "64");
                        return amounts.stream()
                                .filter(s -> s.startsWith(args[2]))
                                .collect(Collectors.toList());
                    }
                    break;

                case "evacuation":
                case "evac":
                    // 撤离点命令的第三个参数：玩家名
                    if (sender.hasPermission("scavenge.admin") && args.length == 3 &&
                        args[1].equalsIgnoreCase("give")) {
                        return getOnlinePlayerNames(args[2]);
                    }
                    break;

                case "crafting":
                case "craft":
                    // 合成系统的三级命令
                    if (args[1].equalsIgnoreCase("test") && sender.hasPermission("scavenge.admin")) {
                        // 合成测试类型补全 - 带中文描述
                        return getCraftingTestCompletions(args[2]);
                    } else if (args[1].equalsIgnoreCase("debug") && sender.hasPermission("scavenge.admin")) {
                        // 调试开关
                        List<String> debugOptions = Arrays.asList("on", "off");
                        return debugOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[2].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "world":
                    // 世界管理的三级命令
                    if (args[1].equalsIgnoreCase("add") || args[1].equalsIgnoreCase("remove")) {
                        // 世界名补全
                        List<String> worldNames = Arrays.asList("world", "world_nether", "world_the_end");
                        return worldNames.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[2].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                default:
                    break;
            }
        }

        return completions;
    }

    /**
     * 获取在线玩家名列表
     */
    private List<String> getOnlinePlayerNames(String partial) {
        return Bukkit.getOnlinePlayers().stream()
                .map(Player::getName)
                .filter(name -> name.toLowerCase().startsWith(partial.toLowerCase()))
                .collect(Collectors.toList());
    }

    /**
     * 获取合成测试类型补全列表（纯中文）
     */
    private List<String> getCraftingTestCompletions(String partial) {
        List<String> chineseTypes = getCraftingTestTypesInChinese();
        List<String> completions = new ArrayList<>();

        for (String chineseType : chineseTypes) {
            // 如果输入匹配中文名称
            if (chineseType.contains(partial) || partial.isEmpty()) {
                completions.add(chineseType);
            }
        }

        return completions;
    }

    /**
     * 获取纯中文的合成测试类型列表
     */
    private List<String> getCraftingTestTypesInChinese() {
        List<String> types = new ArrayList<>();

        // 淬炼石碎片测试
        types.add("普通淬炼石碎片");
        types.add("中等淬炼石碎片");
        types.add("高等淬炼石碎片");
        types.add("上等淬炼石碎片");
        types.add("吞噬石碎片");

        // 强化宝石碎片测试
        types.add("粗糙强化碎片");
        types.add("普通强化碎片");
        types.add("优秀强化碎片");
        types.add("超级强化碎片");
        types.add("符咒碎片");
        types.add("突破碎片");

        return types;
    }

    /**
     * 将中文碎片名称转换为英文键名
     */
    public static String getEnglishKeyFromChinese(String chineseName) {
        switch (chineseName) {
            // 淬炼石碎片
            case "普通淬炼石碎片": return "cuilian_putong";
            case "中等淬炼石碎片": return "cuilian_zhongdeng";
            case "高等淬炼石碎片": return "cuilian_gaodeng";
            case "上等淬炼石碎片": return "cuilian_wanmei";
            case "吞噬石碎片": return "cuilian_huaming";

            // 强化宝石碎片
            case "粗糙强化碎片": return "gem_normal";
            case "普通强化碎片": return "gem_luck";
            case "优秀强化碎片": return "gem_safe";
            case "超级强化碎片": return "gem_vip";
            case "符咒碎片": return "gem_direct";
            case "突破碎片": return "gem_breakthrough";

            default: return chineseName; // 如果没有匹配，返回原始名称
        }
    }
}
