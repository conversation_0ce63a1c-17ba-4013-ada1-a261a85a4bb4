package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.stream.Collectors;

public class ScavengeTabCompleter implements TabCompleter {

    private final ScavengePlugin plugin;

    public ScavengeTabCompleter(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数：主命令
            List<String> subCommands = Arrays.asList("give", "place", "remove", "list", "reset", "reload", "quest",
                    "quests", "leaderboard", "lb", "top", "debug", "test", "world", "setspawn",
                    "zone", "evacuation", "evac", "crafting", "craft");

            // 根据权限过滤命令
            if (sender.hasPermission("scavenge.admin")) {
                completions.addAll(subCommands);
            }

            // 过滤匹配的命令
            return completions.stream()
                    .filter(s -> s.toLowerCase().startsWith(args[0].toLowerCase()))
                    .sorted()
                    .collect(Collectors.toList());
        }

        if (args.length == 2) {
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "give":
                    // 给予命令：补全在线玩家名
                    if (sender.hasPermission("scavenge.admin")) {
                        return getOnlinePlayerNames(args[1]);
                    }
                    break;

                case "reset":
                    // 重置命令：all 或 target
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> resetOptions = Arrays.asList("all", "target");
                        return resetOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "world":
                    // 世界管理命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> worldOptions = Arrays.asList("add", "remove", "list", "enable", "disable");
                        return worldOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "evacuation":
                case "evac":
                case "zone":
                    // 撤离区域管理命令（evacuation重定向到zone）
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> zoneOptions = Arrays.asList("tool", "create", "remove", "list", "reload");
                        return zoneOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "debug":
                    // 调试命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> debugOptions = Arrays.asList("quests", "progress", "stats");
                        return debugOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "test":
                    // 测试命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> testOptions = Arrays.asList("quest", "stats");
                        return testOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "crafting":
                case "craft":
                    // 合成系统命令
                    List<String> craftingOptions = Arrays.asList("info", "recipes", "stats");
                    if (sender.hasPermission("scavenge.admin")) {
                        craftingOptions = Arrays.asList("info", "recipes", "stats", "reload", "save",
                            "enable", "disable", "test", "debug", "verify", "check");
                    }
                    return craftingOptions.stream()
                            .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                            .collect(Collectors.toList());

                default:
                    break;
            }
        }

        if (args.length == 3) {
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "give":
                    // 给予命令的第三个参数：数量（可选）
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> amounts = Arrays.asList("1", "2", "3", "4", "5", "10", "16", "32", "64");
                        return amounts.stream()
                                .filter(s -> s.startsWith(args[2]))
                                .collect(Collectors.toList());
                    }
                    break;

                case "evacuation":
                case "evac":
                    // 撤离点命令的第三个参数：玩家名
                    if (sender.hasPermission("scavenge.admin") && args.length == 3 &&
                        args[1].equalsIgnoreCase("give")) {
                        return getOnlinePlayerNames(args[2]);
                    }
                    break;

                case "crafting":
                case "craft":
                    // 合成系统的三级命令
                    if (args[1].equalsIgnoreCase("test") && sender.hasPermission("scavenge.admin")) {
                        // 合成测试类型补全 - 带中文描述
                        return getCraftingTestCompletions(args[2]);
                    } else if (args[1].equalsIgnoreCase("debug") && sender.hasPermission("scavenge.admin")) {
                        // 调试开关
                        List<String> debugOptions = Arrays.asList("on", "off");
                        return debugOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[2].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "world":
                    // 世界管理的三级命令
                    if (args[1].equalsIgnoreCase("add") || args[1].equalsIgnoreCase("remove")) {
                        // 世界名补全
                        List<String> worldNames = Arrays.asList("world", "world_nether", "world_the_end");
                        return worldNames.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[2].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                default:
                    break;
            }
        }

        return completions;
    }

    /**
     * 获取在线玩家名列表
     */
    private List<String> getOnlinePlayerNames(String partial) {
        return Bukkit.getOnlinePlayers().stream()
                .map(Player::getName)
                .filter(name -> name.toLowerCase().startsWith(partial.toLowerCase()))
                .collect(Collectors.toList());
    }

    /**
     * 获取合成测试类型补全列表（带中文描述）
     */
    private List<String> getCraftingTestCompletions(String partial) {
        Map<String, String> testTypes = getCraftingTestTypes();
        List<String> completions = new ArrayList<>();

        for (Map.Entry<String, String> entry : testTypes.entrySet()) {
            String key = entry.getKey();
            String description = entry.getValue();

            // 如果输入匹配英文键名或中文描述
            if (key.toLowerCase().startsWith(partial.toLowerCase()) ||
                description.contains(partial)) {
                completions.add(key + " §7(" + description + ")");
            }
        }

        return completions;
    }

    /**
     * 获取所有合成测试类型及其中文描述
     */
    private Map<String, String> getCraftingTestTypes() {
        Map<String, String> types = new LinkedHashMap<>();

        // 淬炼石碎片测试
        types.put("cuilian_putong", "普通淬炼石碎片");
        types.put("cuilian_zhongdeng", "中等淬炼石碎片");
        types.put("cuilian_gaodeng", "高等淬炼石碎片");
        types.put("cuilian_wanmei", "上等淬炼石碎片");
        types.put("cuilian_huaming", "吞噬淬炼石碎片");

        // 强化宝石碎片测试
        types.put("gem_normal", "粗糙强化碎片");
        types.put("gem_luck", "普通强化碎片");
        types.put("gem_safe", "优秀强化碎片");
        types.put("gem_vip", "超级强化碎片");
        types.put("gem_direct", "符咒碎片");
        types.put("gem_breakthrough", "突破碎片");

        return types;
    }
}
