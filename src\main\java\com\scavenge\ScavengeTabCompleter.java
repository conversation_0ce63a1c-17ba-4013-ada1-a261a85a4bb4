package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ScavengeTabCompleter implements TabCompleter {

    private final ScavengePlugin plugin;

    public ScavengeTabCompleter(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数：主命令
            List<String> subCommands = Arrays.asList("give", "place", "remove", "list", "reset", "reload", "quest",
                    "quests", "leaderboard", "lb", "top", "world", "telepad", "pad", "setspawn", "evacuation", "evac");

            // 根据权限过滤命令
            if (sender.hasPermission("scavenge.admin")) {
                completions.addAll(subCommands);
            }

            // 过滤匹配的命令
            return completions.stream()
                    .filter(s -> s.toLowerCase().startsWith(args[0].toLowerCase()))
                    .sorted()
                    .collect(Collectors.toList());
        }

        if (args.length == 2) {
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "give":
                    // 给予命令：补全在线玩家名
                    if (sender.hasPermission("scavenge.admin")) {
                        return getOnlinePlayerNames(args[1]);
                    }
                    break;

                case "reset":
                    // 重置命令：all 或 target
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> resetOptions = Arrays.asList("all", "target");
                        return resetOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "world":
                    // 世界管理命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> worldOptions = Arrays.asList("add", "remove", "list", "enable", "disable");
                        return worldOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "telepad":
                case "pad":
                    // 传送垫管理命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> telepadOptions = Arrays.asList("place", "remove", "list");
                        return telepadOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                case "evacuation":
                case "evac":
                    // 撤离点管理命令
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> evacuationOptions = Arrays.asList("give");
                        return evacuationOptions.stream()
                                .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                                .collect(Collectors.toList());
                    }
                    break;

                default:
                    break;
            }
        }

        if (args.length == 3) {
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "give":
                    // 给予命令的第三个参数：数量（可选）
                    if (sender.hasPermission("scavenge.admin")) {
                        List<String> amounts = Arrays.asList("1", "2", "3", "4", "5", "10", "16", "32", "64");
                        return amounts.stream()
                                .filter(s -> s.startsWith(args[2]))
                                .collect(Collectors.toList());
                    }
                    break;

                case "evacuation":
                case "evac":
                    // 撤离点命令的第三个参数：玩家名
                    if (sender.hasPermission("scavenge.admin") && args.length == 3 &&
                        args[1].equalsIgnoreCase("give")) {
                        return getOnlinePlayerNames(args[2]);
                    }
                    break;

                default:
                    break;
            }
        }

        return completions;
    }

    /**
     * 获取在线玩家名列表
     */
    private List<String> getOnlinePlayerNames(String partial) {
        return Bukkit.getOnlinePlayers().stream()
                .map(Player::getName)
                .filter(name -> name.toLowerCase().startsWith(partial.toLowerCase()))
                .collect(Collectors.toList());
    }
}
