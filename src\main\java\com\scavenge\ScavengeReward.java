package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class ScavengeReward {

    public enum RewardType {
        ITEM, // 物品奖励
        COMMAND // 指令奖励
    }

    private final RewardType type;
    private final Material material;
    private final int minAmount;
    private final int maxAmount;
    private final String displayName;
    private final List<String> lore;
    private final List<String> enchantments;
    private final double chance;
    private final int progressTime; // 进度条更新间隔(tick)
    private final Random random;

    // 指令奖励相关
    private final Material displayMaterial;
    private final List<String> commands;
    private final boolean console; // 是否以控制台身份执行指令
    private final boolean isRare; // 是否为稀有奖励

    // 物品奖励构造函数
    public ScavengeReward(Material material, int minAmount, int maxAmount,
            String displayName, List<String> lore, List<String> enchantments, double chance, int progressTime) {
        this.type = RewardType.ITEM;
        this.material = material;
        this.minAmount = minAmount;
        this.maxAmount = maxAmount;
        this.displayName = displayName;
        this.lore = lore != null ? lore : new ArrayList<>();
        this.enchantments = enchantments != null ? enchantments : new ArrayList<>();
        this.chance = chance;
        this.progressTime = progressTime;
        this.random = new Random();
        this.isRare = chance <= 10.0; // 概率小于等于10%视为稀有

        // 指令奖励字段设为默认值
        this.displayMaterial = null;
        this.commands = new ArrayList<>();
        this.console = false;
    }

    // 指令奖励构造函数
    public ScavengeReward(String displayName, Material displayMaterial, double chance,
            List<String> commands, boolean console, int progressTime, boolean isRare) {
        this.type = RewardType.COMMAND;
        this.displayName = displayName;
        this.displayMaterial = displayMaterial;
        this.chance = chance;
        this.commands = commands != null ? commands : new ArrayList<>();
        this.console = console;
        this.progressTime = progressTime;
        this.random = new Random();
        this.isRare = isRare; // 手动指定是否为稀有

        // 物品奖励字段设为默认值
        this.material = displayMaterial;
        this.minAmount = 1;
        this.maxAmount = 1;
        this.lore = new ArrayList<>();
        this.enchantments = new ArrayList<>();
    }

    public double getChance() {
        return chance;
    }

    public Material getMaterial() {
        return material;
    }

    public RewardType getType() {
        return type;
    }

    public Material getDisplayMaterial() {
        return displayMaterial != null ? displayMaterial : material;
    }

    public String getDisplayName() {
        return displayName;
    }

    public int getProgressTime() {
        return progressTime;
    }

    public boolean isRare() {
        return isRare;
    }

    /**
     * 执行奖励 - 根据类型给予物品或执行指令
     */
    public void giveReward(Player player) {
        if (type == RewardType.ITEM) {
            // 物品奖励
            ItemStack item = createItemStack();
            if (item != null) {
                if (player.getInventory().firstEmpty() != -1) {
                    player.getInventory().addItem(item);
                } else {
                    // 背包满了，掉落到地上
                    player.getWorld().dropItemNaturally(player.getLocation(), item);
                }
            }
        } else if (type == RewardType.COMMAND) {
            // 指令奖励
            executeCommands(player);
        }
    }

    /**
     * 执行指令奖励
     */
    private void executeCommands(Player player) {
        String playerName = player.getName();

        // 根据console标志决定执行者
        for (String command : commands) {
            String processedCommand = command.replace("{player}", playerName);
            try {
                if (console) {
                    // 以控制台身份执行指令
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand);
                } else {
                    // 以玩家身份执行指令
                    Bukkit.dispatchCommand(player, processedCommand);
                }
            } catch (Exception e) {
                String executor = console ? "控制台" : "玩家";
            }
        }
    }

    /**
     * 创建显示用的物品（用于GUI显示）
     */
    public ItemStack createDisplayItem() {
        if (type == RewardType.COMMAND) {
            // 指令奖励显示为纸质记录
            ItemStack item = new ItemStack(Material.PAPER, 1);
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName("§a§l指令执行记录");

                // 添加说明文字
                List<String> lore = new ArrayList<>();
                lore.add("§7已执行指令奖励:");
                lore.add("§e" + (displayName != null ? displayName.replace("&", "§") : "未知奖励"));
                lore.add("§7");
                lore.add("§c§l此记录无法领取");
                lore.add("§7指令已自动执行完成");
                meta.setLore(lore);

                item.setItemMeta(meta);
            }
            return item;
        } else {
            // 物品奖励直接返回实际物品
            return createItemStack();
        }
    }

    public ItemStack createItemStack() {
        try {
            int amount = minAmount;
            if (maxAmount > minAmount) {
                amount = random.nextInt(maxAmount - minAmount + 1) + minAmount;
            }

            ItemStack item = new ItemStack(material, amount);
            ItemMeta meta = item.getItemMeta();

            if (meta != null) {
                // 设置显示名称
                if (displayName != null && !displayName.isEmpty()) {
                    meta.setDisplayName(displayName.replace("&", "§"));
                }

                // 设置描述
                if (!lore.isEmpty()) {
                    List<String> coloredLore = new ArrayList<>();
                    for (String line : lore) {
                        coloredLore.add(line.replace("&", "§"));
                    }
                    meta.setLore(coloredLore);
                }

                item.setItemMeta(meta);

                // 添加附魔
                for (String enchantStr : enchantments) {
                    try {
                        String[] parts = enchantStr.split(":");
                        if (parts.length == 2) {
                            String enchantName = parts[0];
                            int level = Integer.parseInt(parts[1]);

                            Enchantment enchant = getEnchantmentByName(enchantName);
                            if (enchant != null) {
                                item.addUnsafeEnchantment(enchant, level);
                            }
                        }
                    } catch (Exception e) {
                        ScavengePlugin.getInstance().getLogger().warning(
                                "无法解析附魔: " + enchantStr);
                    }
                }
            }

            return item;
        } catch (Exception e) {
            ScavengePlugin.getInstance().getLogger().warning(
                    "创建物品时出错: " + e.getMessage());
            return null;
        }
    }

    private Enchantment getEnchantmentByName(String name) {
        // 1.8.8 附魔名称映射
        switch (name.toUpperCase()) {
            case "DAMAGE_ALL":
            case "SHARPNESS":
                return Enchantment.DAMAGE_ALL;
            case "DAMAGE_ARTHROPODS":
            case "BANE_OF_ARTHROPODS":
                return Enchantment.DAMAGE_ARTHROPODS;
            case "DAMAGE_UNDEAD":
            case "SMITE":
                return Enchantment.DAMAGE_UNDEAD;
            case "DIG_SPEED":
            case "EFFICIENCY":
                return Enchantment.DIG_SPEED;
            case "DURABILITY":
            case "UNBREAKING":
                return Enchantment.DURABILITY;
            case "FIRE_ASPECT":
                return Enchantment.FIRE_ASPECT;
            case "KNOCKBACK":
                return Enchantment.KNOCKBACK;
            case "LOOT_BONUS_BLOCKS":
            case "FORTUNE":
                return Enchantment.LOOT_BONUS_BLOCKS;
            case "LOOT_BONUS_MOBS":
            case "LOOTING":
                return Enchantment.LOOT_BONUS_MOBS;
            case "OXYGEN":
            case "RESPIRATION":
                return Enchantment.OXYGEN;
            case "PROTECTION_ENVIRONMENTAL":
            case "PROTECTION":
                return Enchantment.PROTECTION_ENVIRONMENTAL;
            case "PROTECTION_EXPLOSIONS":
            case "BLAST_PROTECTION":
                return Enchantment.PROTECTION_EXPLOSIONS;
            case "PROTECTION_FALL":
            case "FEATHER_FALLING":
                return Enchantment.PROTECTION_FALL;
            case "PROTECTION_FIRE":
            case "FIRE_PROTECTION":
                return Enchantment.PROTECTION_FIRE;
            case "PROTECTION_PROJECTILE":
            case "PROJECTILE_PROTECTION":
                return Enchantment.PROTECTION_PROJECTILE;
            case "SILK_TOUCH":
                return Enchantment.SILK_TOUCH;
            case "WATER_WORKER":
            case "AQUA_AFFINITY":
                return Enchantment.WATER_WORKER;
            case "ARROW_DAMAGE":
            case "POWER":
                return Enchantment.ARROW_DAMAGE;
            case "ARROW_FIRE":
            case "FLAME":
                return Enchantment.ARROW_FIRE;
            case "ARROW_INFINITE":
            case "INFINITY":
                return Enchantment.ARROW_INFINITE;
            case "ARROW_KNOCKBACK":
            case "PUNCH":
                return Enchantment.ARROW_KNOCKBACK;
            default:
                return null;
        }
    }
}
