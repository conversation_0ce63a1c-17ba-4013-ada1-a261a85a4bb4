# 搜刮插件 - 奖励显示中文化更新

## 问题描述
任务界面中的奖励显示为英文，如：
- Beacon → 应显示为"信标"
- Nether Star → 应显示为"下界之星"  
- Diamond Block → 应显示为"钻石块"
- 等等...

## 解决方案

### 1. 扩展材料名称翻译
在 `QuestCategoryGUI.java` 的 `getFriendlyMaterialName()` 方法中添加了更多材料的中文翻译：

#### 新增翻译的材料
- `BEACON` → "信标"
- `NETHER_STAR` → "下界之星"
- `DIAMOND_BLOCK` → "钻石块"
- `EMERALD_BLOCK` → "绿宝石块"
- `GOLD_BLOCK` → "金块"
- `EXPERIENCE_BOTTLE` → "经验瓶"
- `DIAMOND_SWORD` → "钻石剑"
- `GOLDEN_APPLE` → "金苹果"
- `ENDER_PEARL` → "末影珍珠"
- `ENCHANTED_BOOK` → "附魔书"
- `COAL` → "煤炭"
- `COAL_BLOCK` → "煤炭块"
- `STICK` → "木棒"
- `BLAZE_ROD` → "烈焰棒"
- `PAPER` → "纸"
- `SULPHUR` → "火药"
- `WATCH` → "时钟"
- `BOOK` → "书"
- `BOOKSHELF` → "书架"

### 2. 改进广播奖励显示
在 `formatReward()` 方法中改进了广播消息的显示：

#### 智能识别广播内容
- 包含"搜刮大师" → "§e全服广播：搜刮大师荣誉"
- 包含"探索专家" → "§e全服广播：探索专家荣誉"
- 包含"收藏家" → "§e全服广播：收藏家荣誉"
- 包含"宝石猎人" → "§e全服广播：宝石猎人荣誉"
- 包含"寻宝专家" → "§e全服广播：寻宝专家荣誉"
- 包含"传奇搜刮者" → "§c全服广播：传奇搜刮者荣誉"
- 包含"终极收集者" → "§d全服广播：终极收集者荣誉"
- 包含"搜刮宗师" → "§6全服广播：搜刮宗师荣誉"

### 3. 奖励显示格式优化

#### 物品奖励格式
```
§f钻石块 §7x20
§f信标 §7x3
§f下界之星 §7x5
```

#### 金币奖励格式
```
§6金币数量 金币
```

#### 广播奖励格式
```
§e全服广播：具体荣誉称号
```

## 显示效果对比

### 修改前
```
奖励:
- Beacon x3
- Nether Star x5
- Diamond Block x20
- 5000 金币
- 全服广播荣誉
```

### 修改后
```
奖励:
- §f信标 §7x3
- §f下界之星 §7x5
- §f钻石块 §7x20
- §65000 金币
- §c全服广播：传奇搜刮者荣誉
```

## 技术实现细节

### 1. 材料名称映射
使用 switch-case 语句进行精确的材料名称映射，确保：
- 大小写不敏感（使用 `toUpperCase()`）
- 完整的材料覆盖
- 后备格式化方案（`formatMaterialName()`）

### 2. 智能内容识别
通过 `contains()` 方法识别广播内容中的关键词，提供：
- 精确的荣誉称号显示
- 不同等级的颜色区分
- 后备显示方案

### 3. 颜色编码系统
- `§f` - 白色（物品名称）
- `§7` - 灰色（数量）
- `§6` - 金色（金币）
- `§e` - 黄色（普通广播）
- `§c` - 红色（传奇级广播）
- `§d` - 粉色（终极级广播）

## 兼容性保证

### 1. 向后兼容
- 保持原有的奖励解析逻辑
- 未翻译的材料使用格式化后的英文名
- 不影响现有的奖励发放机制

### 2. 扩展性
- 易于添加新的材料翻译
- 支持自定义广播内容识别
- 模块化的格式化方法

## 使用说明

### 1. 立即生效
- 重新编译插件后立即生效
- 无需修改配置文件
- 无需重启服务器

### 2. 查看效果
1. 进入游戏
2. 打开任务界面 (`/scavenge quest`)
3. 选择任意任务分类
4. 查看任务奖励显示

### 3. 添加新翻译
如需添加新的材料翻译，在 `getFriendlyMaterialName()` 方法中添加新的 case：
```java
case "NEW_MATERIAL": return "新材料中文名";
```

## 测试建议

### 1. 功能测试
- 测试所有任务分类的奖励显示
- 验证中文翻译的准确性
- 检查颜色编码的正确性

### 2. 边界测试
- 测试未翻译材料的显示
- 测试特殊字符的处理
- 测试长奖励列表的显示

### 3. 兼容性测试
- 验证与现有系统的兼容性
- 测试奖励发放功能正常
- 确认不影响其他功能

## 预期效果

玩家现在将看到：
- 完全中文化的奖励显示
- 清晰的奖励分类和数量
- 美观的颜色编码
- 直观的荣誉称号显示

这将大大提升玩家的游戏体验和界面友好性！
