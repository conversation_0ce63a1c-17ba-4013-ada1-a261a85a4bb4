# 等级系统配置文件
# 可以自定义等级名称、要求和数量

# 等级配置
# level: 等级数字
# name: 等级名称
# requirement: 所需搜刮次数
# 按照requirement从小到大排序

levels:
  1:
    name: "新手探索者"
    requirement: 0
    description: "刚开始搜刮之旅的新手"
    
  2:
    name: "初级搜刮者"
    requirement: 5
    description: "已经掌握基本搜刮技巧"
    
  3:
    name: "进阶搜刮者"
    requirement: 10
    description: "搜刮技能有所提升"
    
  4:
    name: "活跃搜刮者"
    requirement: 25
    description: "经常进行搜刮活动"
    
  5:
    name: "熟练搜刮者"
    requirement: 50
    description: "搜刮技能已经很熟练"
    
  6:
    name: "搜刮高手"
    requirement: 100
    description: "搜刮技能达到高手水平"
    
  7:
    name: "搜刮达人"
    requirement: 200
    description: "在搜刮方面已是达人级别"
    
  8:
    name: "搜刮专家"
    requirement: 300
    description: "搜刮领域的专家"
    
  9:
    name: "搜刮宗师"
    requirement: 500
    description: "搜刮技艺已达宗师境界"
    
  10:
    name: "搜刮之神"
    requirement: 1000
    description: "搜刮界的传说人物"
    
  # 可以继续添加更多等级
  11:
    name: "超级搜刮王"
    requirement: 2000
    description: "超越常人的搜刮王者"
    
  12:
    name: "终极搜刮神"
    requirement: 5000
    description: "搜刮界的终极存在"

# 等级系统设置
settings:
  # 是否启用等级系统
  enabled: true
  
  # 等级提升时是否发送消息
  level-up-message: true
  
  # 等级提升消息格式
  level-up-format: "§6§l恭喜！§e你升级到了 §f{level} §7({name})§e！"
  
  # 是否在聊天中显示等级前缀
  chat-prefix: true
  
  # 聊天前缀格式
  chat-prefix-format: "§7[§f{level}§7] "
  
  # 是否启用等级奖励
  level-rewards: true

# 等级奖励配置
# 当玩家达到指定等级时给予的奖励
level-rewards:
  2:
    - "give {player} diamond 1"
    - "tell {player} §a恭喜达到初级搜刮者！"
    
  5:
    - "give {player} emerald 3"
    - "give {player} gold_ingot 5"
    - "tell {player} §a恭喜达到熟练搜刮者！"
    
  10:
    - "give {player} diamond_block 1"
    - "give {player} experience_bottle 10"
    - "broadcast §6{player} §e达到了搜刮之神等级！"
    
  # 可以为任何等级添加奖励
  12:
    - "give {player} nether_star 1"
    - "give {player} beacon 1"
    - "broadcast §6§l{player} §e§l成为了终极搜刮神！"

# 等级显示配置
display:
  # 等级在GUI中的显示格式
  gui-format: "§f{level} §7({name})"
  
  # 等级进度显示格式
  progress-format: "§7进度: §f{current}§7/§f{next} §7(还需 §e{remaining}§7 次)"
  
  # 最高等级时的显示
  max-level-format: "§6§l最高等级！"
  
  # 等级描述是否显示在GUI中
  show-description: true
  
  # 描述显示格式
  description-format: "§7{description}"
