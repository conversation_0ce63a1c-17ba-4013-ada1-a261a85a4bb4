package com.scavenge;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.CraftItemEvent;
import org.bukkit.event.inventory.PrepareItemCraftEvent;
import org.bukkit.inventory.CraftingInventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

/**
 * 合成事件监听器
 * 处理自定义物品的合成验证和结果
 */
public class CraftingListener implements Listener {

    private final ScavengePlugin plugin;
    private boolean debugMode = true; // 调试模式开关，默认开启

    public CraftingListener(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    public void setDebugMode(boolean debug) {
        this.debugMode = debug;
    }
    
    /**
     * 准备合成事件 - 验证合成材料
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPrepareItemCraft(PrepareItemCraftEvent event) {
        CraftingInventory inventory = event.getInventory();
        ItemStack[] matrix = inventory.getMatrix();

        // 检查是否包含我们的自定义物品
        if (!containsCustomItems(matrix)) {
            return;
        }

        // 调试信息（仅在调试模式下显示）
        if (debugMode && event.getView().getPlayer() instanceof Player) {
            Player player = (Player) event.getView().getPlayer();
            debugCraftingMatrix(player, matrix);
        }

        // 验证合成配方
        ItemStack result = validateCustomCrafting(matrix);
        if (result != null) {
            inventory.setResult(result);
            if (event.getView().getPlayer() instanceof Player) {
                Player player = (Player) event.getView().getPlayer();
                player.sendMessage("§a检测到有效合成配方！");
            }
        } else {
            inventory.setResult(new ItemStack(Material.AIR));
            // 只有在包含碎片时才显示错误信息
            if (event.getView().getPlayer() instanceof Player) {
                Player player = (Player) event.getView().getPlayer();
                boolean hasFragments = false;
                for (ItemStack item : matrix) {
                    if (item != null && FragmentFactory.isFragment(item)) {
                        hasFragments = true;
                        break;
                    }
                }
                if (hasFragments) {
                    player.sendMessage("§c未找到匹配的合成配方！");
                }
            }
        }
    }
    
    /**
     * 合成完成事件 - 处理合成结果
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onCraftItem(CraftItemEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        CraftingInventory inventory = event.getInventory();
        ItemStack[] matrix = inventory.getMatrix();
        ItemStack result = event.getCurrentItem();
        
        // 检查是否是我们的自定义合成
        if (!containsCustomItems(matrix) || result == null) {
            return;
        }
        
        // 验证合成结果
        if (!isValidCustomCraftResult(matrix, result)) {
            event.setCancelled(true);
            player.sendMessage("§c合成失败！请检查材料是否正确。");
            return;
        }
        
        // 扣除合成材料
        consumeCraftingMaterials(inventory, result);

        // 发送成功消息
        String itemName = result.getItemMeta() != null ? result.getItemMeta().getDisplayName() : "物品";
        if (itemName == null || itemName.isEmpty()) {
            itemName = "强化物品";
        }

        player.sendMessage("§a成功合成了 " + itemName + "！");

        // 记录合成统计
        recordCraftingStats(player, matrix, result, true);

        // 可以在这里添加额外的奖励或效果
        if (isRareCraftResult(result)) {
            player.sendMessage("§6§l恭喜！你合成了稀有物品！");
            // 可以添加特效、经验奖励等
        }
    }
    
    /**
     * 扣除合成材料
     */
    private void consumeCraftingMaterials(CraftingInventory inventory, ItemStack result) {
        ItemStack[] matrix = inventory.getMatrix();

        if (!result.hasItemMeta() || !result.getItemMeta().hasDisplayName()) {
            return;
        }

        String displayName = result.getItemMeta().getDisplayName();

        // 处理特殊合成（需要多种材料）
        if (displayName.contains("符咒") && displayName.contains("淬炼")) {
            // 淬炼符咒：4个高等 + 4个上等 + 1个吞噬
            consumeSpecificFragments(matrix, "高等淬炼石碎片", 4);
            consumeSpecificFragments(matrix, "上等淬炼石碎片", 4);
            consumeSpecificFragments(matrix, "吞噬淬炼石碎片", 1);
        } else if (displayName.contains("直升")) {
            // 强化棒：2个超级 + 2个符咒 + 1个木棍
            consumeSpecificFragments(matrix, "超级强化碎片", 2);
            consumeSpecificFragments(matrix, "符咒碎片", 2);
            consumeSticks(matrix, 1);
        } else {
            // 普通合成（单一材料类型）
            int requiredAmount = getRequiredMaterialAmount(result);
            String targetFragmentType = getTargetFragmentType(result);

            if (requiredAmount > 0 && targetFragmentType != null) {
                consumeSpecificFragments(matrix, targetFragmentType, requiredAmount);
            }
        }

        // 更新工作台矩阵
        inventory.setMatrix(matrix);
    }

    /**
     * 扣除指定类型和数量的碎片
     */
    private void consumeSpecificFragments(ItemStack[] matrix, String fragmentType, int amount) {
        int remainingToConsume = amount;
        for (int i = 0; i < matrix.length && remainingToConsume > 0; i++) {
            ItemStack item = matrix[i];
            if (item != null && isTargetFragment(item, fragmentType)) {
                int currentAmount = item.getAmount();
                if (currentAmount <= remainingToConsume) {
                    // 完全消耗这个槽位
                    matrix[i] = null;
                    remainingToConsume -= currentAmount;
                } else {
                    // 部分消耗这个槽位
                    item.setAmount(currentAmount - remainingToConsume);
                    remainingToConsume = 0;
                }
            }
        }
    }

    /**
     * 扣除木棍
     */
    private void consumeSticks(ItemStack[] matrix, int amount) {
        int remainingToConsume = amount;
        for (int i = 0; i < matrix.length && remainingToConsume > 0; i++) {
            ItemStack item = matrix[i];
            if (item != null && item.getType() == Material.STICK) {
                int currentAmount = item.getAmount();
                if (currentAmount <= remainingToConsume) {
                    // 完全消耗这个槽位
                    matrix[i] = null;
                    remainingToConsume -= currentAmount;
                } else {
                    // 部分消耗这个槽位
                    item.setAmount(currentAmount - remainingToConsume);
                    remainingToConsume = 0;
                }
            }
        }
    }

    /**
     * 检查合成矩阵是否包含自定义物品
     */
    private boolean containsCustomItems(ItemStack[] matrix) {
        for (ItemStack item : matrix) {
            if (item != null && isCustomItem(item)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查物品是否是自定义物品（碎片、淬炼石或宝石）
     */
    private boolean isCustomItem(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (!meta.hasDisplayName()) {
            return false;
        }
        
        return FragmentFactory.isFragment(item) ||
               CuilianStoneFactory.isCuilianStone(item) ||
               GemStoneFactory.isGemStone(item);
    }
    
    /**
     * 验证自定义合成配方
     */
    private ItemStack validateCustomCrafting(ItemStack[] matrix) {
        // 淬炼石碎片合成
        ItemStack cuilianResult = validateCuilianFragmentCrafting(matrix);
        if (cuilianResult != null) {
            return cuilianResult;
        }
        
        // 宝石碎片合成
        ItemStack gemResult = validateGemFragmentCrafting(matrix);
        if (gemResult != null) {
            return gemResult;
        }
        
        // 特殊合成（符咒、直升棒等）
        ItemStack specialResult = validateSpecialCrafting(matrix);
        if (specialResult != null) {
            return specialResult;
        }
        
        return null;
    }
    
    /**
     * 验证淬炼石碎片合成
     */
    private ItemStack validateCuilianFragmentCrafting(ItemStack[] matrix) {
        // 计算每种碎片的数量（考虑堆叠）
        int putongCount = 0, zhongdengCount = 0, gaodengCount = 0, wanmeiCount = 0, huamingCount = 0;

        for (ItemStack item : matrix) {
            if (item != null && FragmentFactory.isFragment(item)) {
                String displayName = item.getItemMeta().getDisplayName();
                int amount = item.getAmount(); // 获取堆叠数量

                if (displayName.contains("普通") && displayName.contains("淬炼石碎片")) {
                    putongCount += amount;
                } else if (displayName.contains("中等")) {
                    zhongdengCount += amount;
                } else if (displayName.contains("高等")) {
                    gaodengCount += amount;
                } else if (displayName.contains("上等")) {
                    wanmeiCount += amount;
                } else if (displayName.contains("吞噬")) {
                    huamingCount += amount;
                }
            }
        }

        // 检查合成配方
        if (putongCount >= 4) return CuilianStoneFactory.createCuilianStone("putong", 1);
        if (zhongdengCount >= 6) return CuilianStoneFactory.createCuilianStone("zhongdeng", 1);
        if (gaodengCount >= 8) return CuilianStoneFactory.createCuilianStone("gaodeng", 1);
        if (wanmeiCount >= 10) return CuilianStoneFactory.createCuilianStone("wanmei", 1);
        if (huamingCount >= 12) return CuilianStoneFactory.createCuilianStone("huaming", 1);

        return null;
    }
    
    /**
     * 验证宝石碎片合成
     */
    private ItemStack validateGemFragmentCrafting(ItemStack[] matrix) {
        // 计算每种碎片的数量（考虑堆叠）
        int normalCount = 0, luckCount = 0, safeCount = 0, vipCount = 0, directCount = 0, breakthroughCount = 0;

        for (ItemStack item : matrix) {
            if (item != null && FragmentFactory.isFragment(item)) {
                String displayName = item.getItemMeta().getDisplayName();
                int amount = item.getAmount(); // 获取堆叠数量

                if (displayName.contains("粗糙")) {
                    normalCount += amount;
                } else if (displayName.contains("普通") && displayName.contains("强化碎片")) {
                    luckCount += amount;
                } else if (displayName.contains("优秀")) {
                    safeCount += amount;
                } else if (displayName.contains("超级")) {
                    vipCount += amount;
                } else if (displayName.contains("【符咒碎片】")) {
                    directCount += amount;
                } else if (displayName.contains("【突破碎片】")) {
                    breakthroughCount += amount;
                }
            }
        }

        // 检查合成配方
        if (normalCount >= 3) return GemStoneFactory.createGemStone("normal", 1, 50);
        if (luckCount >= 4) return GemStoneFactory.createGemStone("luck", 1, 65);
        if (safeCount >= 6) return GemStoneFactory.createGemStone("safe", 1, 70);
        if (vipCount >= 8) return GemStoneFactory.createGemStone("vip", 1, 80);
        if (directCount >= 10) return GemStoneFactory.createGemStone("direct", 29, 80);
        if (breakthroughCount >= 12) return GemStoneFactory.createGemStone("breakthrough", 30, 80);

        return null;
    }
    
    /**
     * 验证特殊合成配方
     */
    private ItemStack validateSpecialCrafting(ItemStack[] matrix) {
        // 检查淬炼符咒合成 (4个高等碎片 + 4个上等碎片 + 1个吞噬碎片)
        int gaodengCount = 0, wanmeiCount = 0, huamingCount = 0;

        for (ItemStack item : matrix) {
            if (item != null && FragmentFactory.isFragment(item)) {
                String displayName = item.getItemMeta().getDisplayName();
                int amount = item.getAmount(); // 获取堆叠数量

                if (displayName.contains("高等")) {
                    gaodengCount += amount;
                } else if (displayName.contains("上等")) {
                    wanmeiCount += amount;
                } else if (displayName.contains("吞噬")) {
                    huamingCount += amount;
                }
            }
        }

        if (gaodengCount >= 4 && wanmeiCount >= 4 && huamingCount >= 1) {
            return CuilianStoneFactory.createCuilianStone("fuzhou", 6);
        }

        // 检查强化棒合成 (2个超级碎片 + 2个符咒碎片 + 1个木棍)
        int vipCount = 0, directCount = 0, stickCount = 0;

        for (ItemStack item : matrix) {
            if (item != null) {
                if (FragmentFactory.isFragment(item)) {
                    String displayName = item.getItemMeta().getDisplayName();
                    int amount = item.getAmount(); // 获取堆叠数量

                    if (displayName.contains("超级")) {
                        vipCount += amount;
                    } else if (displayName.contains("【符咒碎片】")) {
                        directCount += amount;
                    }
                } else if (item.getType() == Material.STICK) {
                    stickCount += item.getAmount();
                }
            }
        }

        if (vipCount >= 2 && directCount >= 2 && stickCount >= 1) {
            return GemStoneFactory.createGemStone("rod", 3, 70);
        }

        return null;
    }
    
    /**
     * 验证合成结果是否有效
     */
    private boolean isValidCustomCraftResult(ItemStack[] matrix, ItemStack result) {
        ItemStack expectedResult = validateCustomCrafting(matrix);
        
        if (expectedResult == null) {
            return false;
        }
        
        // 比较物品类型和显示名称
        if (!expectedResult.getType().equals(result.getType())) {
            return false;
        }
        
        if (expectedResult.hasItemMeta() && result.hasItemMeta()) {
            String expectedName = expectedResult.getItemMeta().getDisplayName();
            String resultName = result.getItemMeta().getDisplayName();
            return expectedName != null && expectedName.equals(resultName);
        }
        
        return !expectedResult.hasItemMeta() && !result.hasItemMeta();
    }
    
    /**
     * 检查是否是稀有合成结果
     */
    private boolean isRareCraftResult(ItemStack result) {
        if (!result.hasItemMeta()) {
            return false;
        }

        String displayName = result.getItemMeta().getDisplayName();
        return displayName != null && (
            displayName.contains("符咒") ||
            displayName.contains("直升") ||
            displayName.contains("突破") ||
            displayName.contains("吞噬")
        );
    }

    /**
     * 记录合成统计
     */
    private void recordCraftingStats(Player player, ItemStack[] matrix, ItemStack result, boolean success) {
        CraftingManager craftingManager = plugin.getCraftingManager();
        if (craftingManager == null || craftingManager.getStatsManager() == null) {
            return;
        }

        // 计算使用的碎片数量
        int fragmentsUsed = 0;
        for (ItemStack item : matrix) {
            if (item != null && FragmentFactory.isFragment(item)) {
                fragmentsUsed += item.getAmount();
            }
        }

        // 确定合成类型
        String craftType = "unknown";
        if (result != null && result.hasItemMeta()) {
            String displayName = result.getItemMeta().getDisplayName();
            if (displayName != null) {
                if (displayName.contains("淬炼")) {
                    craftType = "cuilian";
                } else if (displayName.contains("强化") || displayName.contains("宝石")) {
                    craftType = "gem";
                } else if (displayName.contains("符咒") || displayName.contains("直升") || displayName.contains("突破")) {
                    craftType = "special";
                }
            }
        }

        // 记录统计
        craftingManager.getStatsManager().recordCraftAttempt(player, craftType, success, fragmentsUsed);
    }

    /**
     * 调试合成矩阵
     */
    private void debugCraftingMatrix(Player player, ItemStack[] matrix) {
        player.sendMessage("§e=== 合成调试信息 ===");

        int fragmentCount = 0;
        for (int i = 0; i < matrix.length; i++) {
            ItemStack item = matrix[i];
            if (item != null) {
                if (FragmentFactory.isFragment(item)) {
                    fragmentCount++;
                    String displayName = item.hasItemMeta() ? item.getItemMeta().getDisplayName() : "未知";
                    player.sendMessage("§7位置" + i + ": §a" + displayName + " §7x" + item.getAmount());
                } else {
                    player.sendMessage("§7位置" + i + ": §c" + item.getType().name() + " §7x" + item.getAmount());
                }
            }
        }

        player.sendMessage("§e检测到碎片数量: §b" + fragmentCount);

        // 检查淬炼石碎片
        int putongCount = 0, zhongdengCount = 0, gaodengCount = 0, wanmeiCount = 0, huamingCount = 0;
        for (ItemStack item : matrix) {
            if (item != null && FragmentFactory.isFragment(item)) {
                String displayName = item.getItemMeta().getDisplayName();
                int amount = item.getAmount(); // 获取堆叠数量

                if (displayName.contains("普通") && displayName.contains("淬炼石碎片")) {
                    putongCount += amount;
                } else if (displayName.contains("中等")) {
                    zhongdengCount += amount;
                } else if (displayName.contains("高等")) {
                    gaodengCount += amount;
                } else if (displayName.contains("上等")) {
                    wanmeiCount += amount;
                } else if (displayName.contains("吞噬")) {
                    huamingCount += amount;
                }
            }
        }

        if (putongCount > 0 || zhongdengCount > 0 || gaodengCount > 0 || wanmeiCount > 0 || huamingCount > 0) {
            player.sendMessage("§e淬炼石碎片统计:");
            if (putongCount > 0) player.sendMessage("§7  普通: " + putongCount + "/4");
            if (zhongdengCount > 0) player.sendMessage("§7  中等: " + zhongdengCount + "/6");
            if (gaodengCount > 0) player.sendMessage("§7  高等: " + gaodengCount + "/8");
            if (wanmeiCount > 0) player.sendMessage("§7  上等: " + wanmeiCount + "/10");
            if (huamingCount > 0) player.sendMessage("§7  吞噬: " + huamingCount + "/12");
        }

        // 检查宝石碎片
        int normalCount = 0, luckCount = 0, safeCount = 0, vipCount = 0, directCount = 0, breakthroughCount = 0;
        for (ItemStack item : matrix) {
            if (item != null && FragmentFactory.isFragment(item)) {
                String displayName = item.getItemMeta().getDisplayName();
                int amount = item.getAmount(); // 获取堆叠数量

                if (displayName.contains("粗糙")) {
                    normalCount += amount;
                } else if (displayName.contains("普通") && displayName.contains("强化碎片")) {
                    luckCount += amount;
                } else if (displayName.contains("优秀")) {
                    safeCount += amount;
                } else if (displayName.contains("超级")) {
                    vipCount += amount;
                } else if (displayName.contains("【符咒碎片】")) {
                    directCount += amount;
                } else if (displayName.contains("【突破碎片】")) {
                    breakthroughCount += amount;
                }
            }
        }

        if (normalCount > 0 || luckCount > 0 || safeCount > 0 || vipCount > 0 || directCount > 0 || breakthroughCount > 0) {
            player.sendMessage("§e强化宝石碎片统计:");
            if (normalCount > 0) player.sendMessage("§7  粗糙: " + normalCount + "/3");
            if (luckCount > 0) player.sendMessage("§7  普通: " + luckCount + "/4");
            if (safeCount > 0) player.sendMessage("§7  优秀: " + safeCount + "/6");
            if (vipCount > 0) player.sendMessage("§7  超级: " + vipCount + "/8");
            if (directCount > 0) player.sendMessage("§7  符咒: " + directCount + "/10");
            if (breakthroughCount > 0) player.sendMessage("§7  突破: " + breakthroughCount + "/12");
        }

        player.sendMessage("§e==================");
    }

    /**
     * 获取合成所需的材料数量
     */
    private int getRequiredMaterialAmount(ItemStack result) {
        if (!result.hasItemMeta() || !result.getItemMeta().hasDisplayName()) {
            return 0;
        }

        String displayName = result.getItemMeta().getDisplayName();

        // 淬炼石合成所需数量
        if (displayName.contains("普通") && displayName.contains("淬炼石")) return 4;
        if (displayName.contains("中等") && displayName.contains("淬炼石")) return 6;
        if (displayName.contains("高等") && displayName.contains("淬炼石")) return 8;
        if (displayName.contains("上等") && displayName.contains("淬炼石")) return 10;
        if (displayName.contains("吞噬") && displayName.contains("淬炼石")) return 12;

        // 宝石合成所需数量
        if (displayName.contains("粗糙") && displayName.contains("强化")) return 3;
        if (displayName.contains("普通") && displayName.contains("强化")) return 4;
        if (displayName.contains("优秀") && displayName.contains("强化")) return 6;
        if (displayName.contains("超级") && displayName.contains("强化")) return 8;
        if (displayName.contains("符咒") && displayName.contains("强化")) return 10;
        if (displayName.contains("突破") && displayName.contains("强化")) return 12;

        // 特殊合成
        if (displayName.contains("符咒") && displayName.contains("淬炼")) return 9; // 4+4+1
        if (displayName.contains("直升")) return 5; // 2+2+1

        return 0;
    }

    /**
     * 获取目标碎片类型
     */
    private String getTargetFragmentType(ItemStack result) {
        if (!result.hasItemMeta() || !result.getItemMeta().hasDisplayName()) {
            return null;
        }

        String displayName = result.getItemMeta().getDisplayName();

        // 淬炼石对应的碎片类型
        if (displayName.contains("普通") && displayName.contains("淬炼石")) return "普通淬炼石碎片";
        if (displayName.contains("中等") && displayName.contains("淬炼石")) return "中等淬炼石碎片";
        if (displayName.contains("高等") && displayName.contains("淬炼石")) return "高等淬炼石碎片";
        if (displayName.contains("上等") && displayName.contains("淬炼石")) return "上等淬炼石碎片";
        if (displayName.contains("吞噬") && displayName.contains("淬炼石")) return "吞噬淬炼石碎片";

        // 宝石对应的碎片类型
        if (displayName.contains("粗糙") && displayName.contains("强化")) return "粗糙强化碎片";
        if (displayName.contains("普通") && displayName.contains("强化")) return "普通强化碎片";
        if (displayName.contains("优秀") && displayName.contains("强化")) return "优秀强化碎片";
        if (displayName.contains("超级") && displayName.contains("强化")) return "超级强化碎片";
        if (displayName.contains("符咒") && displayName.contains("强化")) return "符咒碎片";
        if (displayName.contains("突破") && displayName.contains("强化")) return "突破碎片";

        return null;
    }

    /**
     * 检查是否是目标碎片
     */
    private boolean isTargetFragment(ItemStack item, String targetType) {
        if (!FragmentFactory.isFragment(item) || !item.hasItemMeta()) {
            return false;
        }

        String displayName = item.getItemMeta().getDisplayName();
        if (displayName == null) {
            return false;
        }

        // 精确匹配碎片类型
        switch (targetType) {
            // 淬炼石碎片
            case "普通淬炼石碎片":
                return displayName.contains("普通") && displayName.contains("淬炼石碎片");
            case "中等淬炼石碎片":
                return displayName.contains("中等") && displayName.contains("淬炼石碎片");
            case "高等淬炼石碎片":
                return displayName.contains("高等") && displayName.contains("淬炼石碎片");
            case "上等淬炼石碎片":
                return displayName.contains("上等") && displayName.contains("淬炼石碎片");
            case "吞噬淬炼石碎片":
                return displayName.contains("吞噬") && displayName.contains("淬炼石碎片");

            // 强化碎片
            case "粗糙强化碎片":
                return displayName.contains("粗糙") && displayName.contains("强化碎片");
            case "普通强化碎片":
                return displayName.contains("普通") && displayName.contains("强化碎片");
            case "优秀强化碎片":
                return displayName.contains("优秀") && displayName.contains("强化碎片");
            case "超级强化碎片":
                return displayName.contains("超级") && displayName.contains("强化碎片");
            case "符咒碎片":
                return displayName.contains("【符咒碎片】");
            case "突破碎片":
                return displayName.contains("【突破碎片】");

            default:
                return false;
        }
    }
}
