package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.Recipe;
import org.bukkit.inventory.ShapedRecipe;
import org.bukkit.inventory.ShapelessRecipe;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 合成系统管理器
 * 管理淬炼石和宝石的合成配方
 */
public class CraftingManager {
    
    private final JavaPlugin plugin;
    private final List<NamespacedKey> registeredRecipes;
    
    public CraftingManager(JavaPlugin plugin) {
        this.plugin = plugin;
        this.registeredRecipes = new ArrayList<>();
    }
    
    /**
     * 注册所有合成配方
     */
    public void registerAllRecipes() {
        // 注册淬炼石合成配方
        registerCuilianStoneRecipes();
        
        // 注册宝石合成配方
        registerGemStoneRecipes();
        
        plugin.getLogger().info("已注册 " + registeredRecipes.size() + " 个合成配方");
    }
    
    /**
     * 注册淬炼石合成配方
     */
    private void registerCuilianStoneRecipes() {
        // 普通淬炼石 = 4个普通淬炼石碎片
        registerFragmentToCuilianRecipe("putong", 4, "normal_cuilian");
        
        // 中等淬炼石 = 6个中等淬炼石碎片
        registerFragmentToCuilianRecipe("zhongdeng", 6, "medium_cuilian");
        
        // 高等淬炼石 = 8个高等淬炼石碎片
        registerFragmentToCuilianRecipe("gaodeng", 8, "high_cuilian");
        
        // 上等淬炼石 = 10个上等淬炼石碎片
        registerFragmentToCuilianRecipe("wanmei", 10, "perfect_cuilian");
        
        // 淬炼吞噬石 = 12个吞噬石碎片
        registerFragmentToCuilianRecipe("huaming", 12, "devouring_cuilian");
        
        // 淬炼符咒 = 2个高等碎片 + 2个上等碎片 + 1个吞噬碎片
        registerCuilianCharmRecipe();
        
        // 淬炼直升棒 = 1个淬炼符咒 + 4个吞噬碎片
        registerCuilianRodRecipe();
    }
    
    /**
     * 注册宝石合成配方
     */
    private void registerGemStoneRecipes() {
        // 粗糙强化素材 = 3个粗糙强化碎片
        registerFragmentToGemRecipe("normal", 3, "normal_gem");
        
        // 普通强化素材 = 4个普通强化碎片
        registerFragmentToGemRecipe("luck", 4, "luck_gem");
        
        // 优秀强化素材 = 6个优秀强化碎片
        registerFragmentToGemRecipe("safe", 6, "safe_gem");
        
        // 超级强化素材 = 8个超级强化碎片
        registerFragmentToGemRecipe("vip", 8, "vip_gem");
        
        // 强化直升符咒 = 10个符咒碎片
        registerFragmentToGemRecipe("direct", 10, "direct_gem");
        
        // 强化突破石 = 12个突破碎片
        registerFragmentToGemRecipe("breakthrough", 12, "breakthrough_gem");
        
        // 强化棒 = 2个超级强化碎片 + 2个符咒碎片
        registerGemRodRecipe();
    }
    
    /**
     * 注册碎片到淬炼石的合成配方
     */
    private void registerFragmentToCuilianRecipe(String type, int fragmentCount, String recipeKey) {
        try {
            NamespacedKey key = new NamespacedKey(plugin, recipeKey);
            
            // 创建结果物品
            ItemStack result = CuilianStoneFactory.createCuilianStone(type, 1);
            
            // 创建无序合成配方
            ShapelessRecipe recipe = new ShapelessRecipe(key, result);
            
            // 添加碎片材料
            ItemStack fragment = FragmentFactory.createCuilianFragment(type);
            for (int i = 0; i < fragmentCount; i++) {
                recipe.addIngredient(fragment.getType());
            }
            
            // 注册配方
            Bukkit.addRecipe(recipe);
            registeredRecipes.add(key);
            
        } catch (Exception e) {
            plugin.getLogger().warning("注册淬炼石合成配方失败: " + type + " - " + e.getMessage());
        }
    }
    
    /**
     * 注册碎片到宝石的合成配方
     */
    private void registerFragmentToGemRecipe(String type, int fragmentCount, String recipeKey) {
        try {
            NamespacedKey key = new NamespacedKey(plugin, recipeKey);
            
            // 创建结果物品
            ItemStack result = GemStoneFactory.createGemStone(type, 29, 80);
            
            // 创建无序合成配方
            ShapelessRecipe recipe = new ShapelessRecipe(key, result);
            
            // 添加碎片材料
            ItemStack fragment = FragmentFactory.createGemFragment(type);
            for (int i = 0; i < fragmentCount; i++) {
                recipe.addIngredient(fragment.getType());
            }
            
            // 注册配方
            Bukkit.addRecipe(recipe);
            registeredRecipes.add(key);
            
        } catch (Exception e) {
            plugin.getLogger().warning("注册宝石合成配方失败: " + type + " - " + e.getMessage());
        }
    }
    
    /**
     * 注册淬炼符咒合成配方
     */
    private void registerCuilianCharmRecipe() {
        try {
            NamespacedKey key = new NamespacedKey(plugin, "cuilian_charm");
            
            // 创建结果物品
            ItemStack result = CuilianStoneFactory.createCuilianStone("fuzhou", 6);
            
            // 创建有序合成配方 (3x3)
            ShapedRecipe recipe = new ShapedRecipe(key, result);
            recipe.shape("ABC", "DEF", "GHI");
            
            // 设置材料
            recipe.setIngredient('A', FragmentFactory.createCuilianFragment("gaodeng").getType());
            recipe.setIngredient('B', FragmentFactory.createCuilianFragment("wanmei").getType());
            recipe.setIngredient('C', FragmentFactory.createCuilianFragment("gaodeng").getType());
            recipe.setIngredient('D', FragmentFactory.createCuilianFragment("wanmei").getType());
            recipe.setIngredient('E', FragmentFactory.createCuilianFragment("huaming").getType());
            recipe.setIngredient('F', FragmentFactory.createCuilianFragment("wanmei").getType());
            recipe.setIngredient('G', FragmentFactory.createCuilianFragment("gaodeng").getType());
            recipe.setIngredient('H', FragmentFactory.createCuilianFragment("wanmei").getType());
            recipe.setIngredient('I', FragmentFactory.createCuilianFragment("gaodeng").getType());
            
            // 注册配方
            Bukkit.addRecipe(recipe);
            registeredRecipes.add(key);
            
        } catch (Exception e) {
            plugin.getLogger().warning("注册淬炼符咒合成配方失败: " + e.getMessage());
        }
    }
    
    /**
     * 注册淬炼直升棒合成配方
     */
    private void registerCuilianRodRecipe() {
        try {
            NamespacedKey key = new NamespacedKey(plugin, "cuilian_rod");
            
            // 创建结果物品
            ItemStack result = CuilianStoneFactory.createCuilianStone("rod", 3);
            
            // 创建有序合成配方
            ShapedRecipe recipe = new ShapedRecipe(key, result);
            recipe.shape(" A ", "BCB", " D ");
            
            // 设置材料 (1个符咒 + 4个吞噬碎片)
            recipe.setIngredient('A', FragmentFactory.createCuilianFragment("huaming").getType());
            recipe.setIngredient('B', FragmentFactory.createCuilianFragment("huaming").getType());
            recipe.setIngredient('C', CuilianStoneFactory.createCuilianStone("fuzhou", 6).getType());
            recipe.setIngredient('D', FragmentFactory.createCuilianFragment("huaming").getType());
            
            // 注册配方
            Bukkit.addRecipe(recipe);
            registeredRecipes.add(key);
            
        } catch (Exception e) {
            plugin.getLogger().warning("注册淬炼直升棒合成配方失败: " + e.getMessage());
        }
    }
    
    /**
     * 注册强化棒合成配方
     */
    private void registerGemRodRecipe() {
        try {
            NamespacedKey key = new NamespacedKey(plugin, "gem_rod");
            
            // 创建结果物品
            ItemStack result = GemStoneFactory.createGemStone("rod", 3, 70);
            
            // 创建有序合成配方
            ShapedRecipe recipe = new ShapedRecipe(key, result);
            recipe.shape(" A ", "BCB", " A ");
            
            // 设置材料 (2个超级碎片 + 2个符咒碎片)
            recipe.setIngredient('A', FragmentFactory.createGemFragment("vip").getType());
            recipe.setIngredient('B', FragmentFactory.createGemFragment("direct").getType());
            recipe.setIngredient('C', Material.STICK); // 中心使用木棍
            
            // 注册配方
            Bukkit.addRecipe(recipe);
            registeredRecipes.add(key);
            
        } catch (Exception e) {
            plugin.getLogger().warning("注册强化棒合成配方失败: " + e.getMessage());
        }
    }
    
    /**
     * 移除所有注册的合成配方
     */
    public void unregisterAllRecipes() {
        for (NamespacedKey key : registeredRecipes) {
            try {
                Iterator<Recipe> iterator = Bukkit.recipeIterator();
                while (iterator.hasNext()) {
                    Recipe recipe = iterator.next();
                    if (recipe instanceof ShapedRecipe) {
                        ShapedRecipe shapedRecipe = (ShapedRecipe) recipe;
                        if (shapedRecipe.getKey().equals(key)) {
                            iterator.remove();
                            break;
                        }
                    } else if (recipe instanceof ShapelessRecipe) {
                        ShapelessRecipe shapelessRecipe = (ShapelessRecipe) recipe;
                        if (shapelessRecipe.getKey().equals(key)) {
                            iterator.remove();
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warning("移除合成配方失败: " + key + " - " + e.getMessage());
            }
        }
        
        registeredRecipes.clear();
        plugin.getLogger().info("已移除所有合成配方");
    }
    
    /**
     * 获取已注册的配方数量
     */
    public int getRegisteredRecipeCount() {
        return registeredRecipes.size();
    }
}
