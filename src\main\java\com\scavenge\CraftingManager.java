package com.scavenge;

import org.bukkit.plugin.java.JavaPlugin;

/**
 * 合成系统管理器
 * 管理淬炼石和宝石的合成配方
 * 简化版本，适用于1.8.8
 */
public class CraftingManager {

    private final JavaPlugin plugin;
    private int registeredRecipeCount;

    public CraftingManager(JavaPlugin plugin) {
        this.plugin = plugin;
        this.registeredRecipeCount = 0;
    }

    /**
     * 注册所有合成配方
     */
    public void registerAllRecipes() {
        // 在1.8.8中，我们主要依靠事件监听器来处理自定义合成
        // 这里只是记录配方数量
        registeredRecipeCount = 15; // 预计的配方数量

        plugin.getLogger().info("合成系统已启用，支持 " + registeredRecipeCount + " 种合成配方");
        plugin.getLogger().info("合成配方将通过工作台事件监听器处理");
    }

    /**
     * 移除所有注册的合成配方
     */
    public void unregisterAllRecipes() {
        plugin.getLogger().info("合成系统已禁用");
    }

    /**
     * 获取已注册的配方数量
     */
    public int getRegisteredRecipeCount() {
        return registeredRecipeCount;
    }
}
