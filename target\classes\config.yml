# 搜刮插件配置文件
# Scavenge Plugin Configuration

# 搜刮方块设置
scavenge-block:
  # 搜刮方块的材质类型 (CHEST, ENDER_CHEST, TRAPPED_CHEST)
  material: CHEST
  # 搜刮方块的显示名称
  display-name: "&6&l搜刮箱"
  # 搜刮方块的描述
  lore:
    - "&7右键打开进行搜刮"
    - "&7每次搜刮都有不同的奖励"
    - "&c&l一次性使用"

# 搜刮箱设置
scavenge-chest:
  # 重置时间 (秒)
  reset-time: 300
  # 冷却消息
  cooldown-message: "&c这个搜刮箱还需要等待 {time} 秒才能重新搜刮!"
  # 可用消息
  available-message: "&a搜刮箱已重置，可以重新搜刮了!"

  # 全息图设置
  hologram:
    # 是否启用全息图
    enabled: true
    # 全息图高度偏移 (方块上方多少格)
    height-offset: 1.5
    # 全息图文本格式
    text-format: "&c&l重置倒计时: &f{time}"
    # 更新间隔 (秒)
    update-interval: 1
    # 全息图可见距离 (方块)
    view-distance: 16

# GUI设置
gui:
  # GUI标题
  title: "&6&l搜刮奖励"
  # GUI大小 (9, 18, 27, 36, 45, 54)
  size: 27
  # 默认随机显示的物品数量 (1-27) - 当玩家没有特殊权限时使用
  random-items: 5

  # 权限控制的物品数量设置
  permission-items:
    # VIP权限 - 更多物品
    "scavenge.vip":
      random-items: 8
      display-name: "&6&lVIP搜刮奖励"
    # SVIP权限 - 更多物品
    "scavenge.svip":
      random-items: 12
      display-name: "&e&lSVIP搜刮奖励"
    # MVP权限 - 最多物品
    "scavenge.mvp":
      random-items: 15
      display-name: "&c&lMVP搜刮奖励"
    # 管理员权限 - 满箱物品
    "scavenge.admin":
      random-items: 20
      display-name: "&4&l管理员搜刮奖励"
  # 未搜索物品显示
  unsearched:
    material: STAINED_GLASS_PANE
    data: 7  # 灰色玻璃板
    display-name: "&7未探索"
  # 搜索中物品显示
  searching:
    material: STAINED_GLASS_PANE
    data: 14  # 红色玻璃板
    display-name: "&c&l探索中 {progress}%"
  # 搜索动画设置
  animation:
    # 更新间隔 (tick, 20tick = 1秒) - 3tick确保稳定显示每个数字
    # 总时间 = 100步 × update-interval tick = 100 × 3 = 300tick = 15秒
    update-interval: 3
    # 自动搜索下一个的延迟 (tick)
    next-search-delay: 40
    # 进度条方向设置
    progress-direction:
      # 进度条方向: "countdown" (100%->0%) 或 "countup" (0%->100%)
      mode: "countup"
      # countdown模式: 倒计时，从100%递减到0%，营造紧张感
      # countup模式: 正计时，从0%递增到100%，营造期待感
    # 音效配置
    sounds:
      # 倒计时音效 (每个数字递减时播放) - 与进度条完全同步
      countdown:
        sound: "CLICK"
        volume: 0.3  # 降低音量，因为会频繁播放
        pitch: 1.2   # 稍微提高音调，让声音更清脆
      # 完成音效 (进度条到0%变成物品时播放) - 铁砧放置音效
      complete:
        sound: "ANVIL_LAND"
        volume: 1.0
        pitch: 1.0

# 搜刮奖励物品列表
rewards:
  items:
    # 钻石
    diamond:
      material: DIAMOND
      amount: 1-3
      display-name: "&b钻石"
      chance: 20.0  # 20% 几率
      progress-time: 2  # 进度条更新间隔(tick) - 中等速度
    # 铁锭
    iron:
      material: IRON_INGOT
      amount: 2-8
      display-name: "&7铁锭"
      chance: 25.0  # 25% 几率
      progress-time: 1  # 进度条更新间隔(tick) - 快速
    # 金锭
    gold:
      material: GOLD_INGOT
      amount: 1-5
      display-name: "&6金锭"
      chance: 20.0  # 20% 几率
      progress-time: 3  # 进度条更新间隔(tick) - 慢速
    # 绿宝石
    emerald:
      material: EMERALD
      amount: 1-2
      display-name: "&a绿宝石"
      chance: 15.0  # 15% 几率
      progress-time: 4  # 进度条更新间隔(tick) - 较慢
    # 面包
    bread:
      material: BREAD
      amount: 3-10
      display-name: "&e面包"
      chance: 18.5  # 18.5% 几率
      progress-time: 2  # 进度条更新间隔(tick) - 中等速度
    # 经验奖励
    experience:
      material: EXPERIENCE_BOTTLE
      amount: 5-10
      display-name: "&a经验瓶"
      chance: 10.0  # 10% 几率
      progress-time: 5  # 进度条更新间隔(tick) - 慢速
    # 指令奖励 - 给予金币 (控制台执行)
    money_reward:
      type: "COMMAND"
      display-name: "&6金币奖励"
      display-material: GOLD_NUGGET
      chance: 8.0  # 8% 几率
      console: true  # 是否以控制台身份执行指令
      progress-time: 6  # 进度条更新间隔(tick) - 很慢
      commands:
        - "eco give {player} 100"  # 给玩家100金币
        - "broadcast {player} 获得了100金币奖励！"  # 广播消息
    # 指令奖励 - 传送到随机位置 (玩家执行)
    teleport_reward:
      type: "COMMAND"
      display-name: "&5随机传送"
      display-material: ENDER_PEARL
      chance: 5.0  # 5% 几率
      console: false  # 玩家身份执行指令
      progress-time: 7  # 进度条更新间隔(tick) - 非常慢
      commands:
        - "rtp"  # 玩家执行随机传送
    # 指令奖励 - 管理员权限指令 (控制台执行)
    admin_reward:
      type: "COMMAND"
      display-name: "&c&l管理员奖励"
      display-material: DIAMOND
      chance: 2.0  # 2% 几率
      console: true  # 控制台执行，可以使用管理员权限
      progress-time: 10  # 进度条更新间隔(tick) - 极慢
      commands:
        - "give {player} diamond 5"  # 给予5个钻石
        - "tell {player} &a你获得了管理员特殊奖励！"

    # ==================== 新增指令物品奖励 ====================
    # 特殊武器奖励
    legendary_sword:
      type: "COMMAND"
      display-name: "&6&l传说之剑"
      display-material: DIAMOND_SWORD
      chance: 1.0  # 1% 几率
      console: true
      progress-time: 12  # 进度条更新间隔(tick) - 超慢
      commands:
        - "give {player} diamond_sword 1 0 {Unbreakable:1,display:{Name:\"&6&l传说之剑\",Lore:[\"&7一把传说中的神剑\",\"&7拥有无尽的力量\"]},Enchantments:[{id:16,lvl:5},{id:19,lvl:2},{id:21,lvl:3}]}"
        - "broadcast &6{player} &e获得了传说之剑！"

    # 魔法苹果奖励
    magic_apple:
      type: "COMMAND"
      display-name: "&6&l魔法苹果"
      display-material: GOLDEN_APPLE
      chance: 3.0  # 3% 几率
      console: true
      progress-time: 8
      commands:
        - "give {player} golden_apple 1 0 {display:{Name:\"&6&l魔法苹果\",Lore:[\"&7蕴含魔法力量的苹果\",\"&7食用后获得强大效果\"]}}"
        - "tell {player} &a你获得了魔法苹果！"

    # 幸运钥匙奖励
    lucky_key:
      type: "COMMAND"
      display-name: "&e&l幸运钥匙"
      display-material: TRIPWIRE_HOOK
      chance: 4.0  # 4% 几率
      console: true
      progress-time: 6
      commands:
        - "give {player} tripwire_hook 1 0 {display:{Name:\"&e&l幸运钥匙\",Lore:[\"&7可以打开特殊宝箱\",\"&7里面有丰富的奖励\"]}}"
        - "tell {player} &e你获得了幸运钥匙！"

    # 经验宝石奖励
    exp_gem:
      type: "COMMAND"
      display-name: "&b&l经验宝石"
      display-material: EMERALD
      chance: 2.5  # 2.5% 几率
      console: true
      progress-time: 9
      commands:
        - "give {player} emerald 1 0 {display:{Name:\"&b&l经验宝石\",Lore:[\"&7蕴含大量经验的宝石\",\"&7右键使用获得经验\"]}}"
        - "give {player} experience_bottle 10"
        - "tell {player} &b你获得了经验宝石和10个经验瓶！"

    # 飞行羽毛奖励
    flight_feather:
      type: "COMMAND"
      display-name: "&f&l飞行羽毛"
      display-material: FEATHER
      chance: 1.5  # 1.5% 几率
      console: true
      progress-time: 15
      commands:
        - "give {player} feather 1 0 {display:{Name:\"&f&l飞行羽毛\",Lore:[\"&7神奇的羽毛\",\"&7可以短暂获得飞行能力\"]}}"
        - "effect {player} 11 30 2"  # 给予30秒抗性提升II
        - "tell {player} &f你获得了飞行羽毛和抗性效果！"

    # 财富符咒奖励
    wealth_charm:
      type: "COMMAND"
      display-name: "&6&l财富符咒"
      display-material: GOLD_NUGGET
      chance: 3.5  # 3.5% 几率
      console: true
      progress-time: 7
      commands:
        - "give {player} gold_nugget 1 0 {display:{Name:\"&6&l财富符咒\",Lore:[\"&7带来财富的神秘符咒\",\"&7持有者将获得金钱\"]}}"
        - "eco give {player} 500"
        - "tell {player} &6你获得了财富符咒和500金币！"

    # 治疗药水奖励
    healing_potion:
      type: "COMMAND"
      display-name: "&c&l治疗药水"
      display-material: POTION
      chance: 5.0  # 5% 几率
      console: true
      progress-time: 5
      commands:
        - "give {player} potion 1 8261 {display:{Name:\"&c&l治疗药水\",Lore:[\"&7强效的治疗药水\",\"&7瞬间恢复生命值\"]}}"
        - "effect {player} 10 10 2"  # 给予10秒再生II
        - "tell {player} &c你获得了治疗药水和再生效果！"

    # 空奖励 (什么都不给)
    empty:
      material: AIR
      amount: 0
      display-name: "&7空"
      chance: 1.0  # 1% 几率 (降低了空奖励概率)
      progress-time: 8  # 进度条更新间隔(tick) - 很慢

# 世界限制设置
world-restrictions:
  # 是否启用世界限制
  enabled: true
  # 允许使用搜刮箱的世界列表
  allowed-worlds:
    - "world"
    - "world_nether"
    - "world_the_end"
  # 在限制世界中禁止使用的指令列表
  blocked-commands:
    - "spawn"
    - "home"
    - "tp"
    - "teleport"
    - "warp"
    - "back"
    - "tpa"
    - "tpaccept"
    - "tphere"
    - "fly"
    - "gamemode"
    - "gm"

# 消息配置
messages:
  no-permission: "&c你没有权限使用这个命令!"
  player-not-found: "&c找不到玩家: {player}"
  scavenge-given: "&a已给予 {player} 一个搜刮方块!"
  scavenge-received: "&a你收到了一个搜刮方块! 右键放置后可以搜刮!"
  config-reloaded: "&a配置文件已重新加载!"
  block-placed: "&a搜刮箱已放置! 右键点击开始搜刮!"
  scavenge-complete: "&a搜刮完成! 你获得了一些奖励!"
  inventory-full: "&c你的背包已满! 请清理后再试!"
  exploration-started: "&e开始探索新的物品..."
  exploration-complete-found: "&a探索完成！发现了: {item} x{amount}"
  exploration-complete-empty: "&7探索完成！这次没有发现任何物品。"
  all-items-claimed: "&a所有物品都已搜刮完成！等待重置时间后可再次搜刮。"
  force-reset-message: "&c搜刮箱已重置，GUI已关闭。"
  chest-in-use: "&c这个搜刮箱正在被其他玩家使用中!"
  no-admin-permission: "&c只有管理员可以放置或破坏搜刮箱!"
  chest-removed: "&a搜刮箱已成功移除!"
  quest-completed: "&a恭喜! 你完成了任务: {quest}"
  quest-reward-claimed: "&a你已成功领取任务 {quest} 的奖励!"
  world-not-allowed: "&c搜刮箱只能在指定世界中使用!"
  command-blocked: "&c在搜刮世界中禁止使用此指令!"
  chest-over-completed: "&c&l权限不足！&r&e你的权限已降级，此搜刮箱已超出你的权限范围。请等待 &c{time}秒 &e后重置。"

# 传送垫设置
teleport-pad:
  # 是否启用传送垫功能
  enabled: true
  # 传送垫材质
  material: GOLD_PLATE
  # 传送垫显示名称
  display-name: "&6&l传送垫"
  # 传送垫描述
  lore:
    - "&7站在上面开始传送倒计时"
    - "&7倒计时结束后传送到主城"

  # 撤离点物品设置
  evacuation-item:
    # 撤离点物品材质
    material: GOLD_PLATE
    # 撤离点物品显示名称
    display-name: "&6&l撤离点"
    # 撤离点物品描述
    lore:
      - "&7右键放置创建撤离点"
      - "&7玩家站在上面可以传送到主城"
      - "&e&l管理员专用物品"
    # 撤离点物品发光效果
    enchanted: true
  # 倒计时时间 (秒)
  countdown-time: 5
  # 倒计时期间是否允许移动
  allow-movement: false
  # 倒计时取消距离 (格子，玩家离开传送垫多远取消传送)
  cancel-distance: 2.0
  # 倒计时显示设置
  countdown-display:
    # 标题显示
    title:
      enabled: true
      format: "&6&l传送倒计时"
    # 副标题显示
    subtitle:
      enabled: true
      format: "&e{time} 秒后传送到主城"
    # 聊天消息
    chat:
      enabled: true
      start-message: "&a开始传送倒计时，请保持站立..."
      cancel-message: "&c传送已取消！"
      success-message: "&a传送成功！"
  # 音效设置
  sounds:
    # 开始倒计时音效
    start:
      sound: "NOTE_PLING"
      volume: 1.0
      pitch: 1.0
    # 倒计时每秒音效
    tick:
      sound: "NOTE_STICKS"
      volume: 0.5
      pitch: 1.5
    # 传送成功音效
    success:
      sound: "ENDERMAN_TELEPORT"
      volume: 1.0
      pitch: 1.0
    # 传送取消音效
    cancel:
      sound: "NOTE_BASS"
      volume: 1.0
      pitch: 0.5

  # 粒子效果设置
  particles:
    # 是否启用粒子效果
    enabled: true
    # 粒子类型 (VILLAGER_HAPPY, PORTAL, ENCHANTMENT_TABLE, FLAME, etc.)
    type: "VILLAGER_HAPPY"
    # 粒子数量
    count: 3
    # 粒子范围 (X, Y, Z 偏移)
    offset:
      x: 0.5
      y: 0.1
      z: 0.5
    # 粒子速度
    speed: 0.0
    # 粒子更新间隔 (tick, 20tick = 1秒)
    update-interval: 10
    # 粒子可见距离 (方块)
    view-distance: 32

# 任务系统已移至单独的 quests.yml 配置文件
# 请查看 quests.yml 文件来配置任务
