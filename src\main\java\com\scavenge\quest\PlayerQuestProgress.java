package com.scavenge.quest;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 玩家任务进度类
 */
public class PlayerQuestProgress {

    private UUID playerId;
    private String questId;
    private int currentProgress;
    private boolean completed;
    private boolean claimed;
    private long completedTime;

    public PlayerQuestProgress(UUID playerId, String questId) {
        this.playerId = playerId;
        this.questId = questId;
        this.currentProgress = 0;
        this.completed = false;
        this.claimed = false;
        this.completedTime = 0;
    }

    /**
     * 增加进度
     */
    public void addProgress(int amount) {
        this.currentProgress += amount;
    }

    /**
     * 设置进度
     */
    public void setProgress(int progress) {
        this.currentProgress = progress;
    }

    /**
     * 检查是否完成任务
     */
    public boolean checkCompletion(int targetAmount) {
        if (!completed && currentProgress >= targetAmount) {
            completed = true;
            completedTime = System.currentTimeMillis();
            // 确保进度不超过目标值，但至少等于目标值
            if (currentProgress > targetAmount) {
                currentProgress = targetAmount;
            }
            return true;
        }
        return false;
    }

    /**
     * 获取进度百分比
     */
    public double getProgressPercentage(int targetAmount) {
        if (targetAmount <= 0)
            return 100.0;
        return Math.min(100.0, (double) currentProgress / targetAmount * 100.0);
    }

    /**
     * 获取进度条字符串
     */
    public String getProgressBar(int targetAmount, int barLength) {
        double percentage = getProgressPercentage(targetAmount);
        int filledLength = (int) (barLength * percentage / 100.0);

        StringBuilder bar = new StringBuilder("&a");
        for (int i = 0; i < filledLength; i++) {
            bar.append("█");
        }
        bar.append("&7");
        for (int i = filledLength; i < barLength; i++) {
            bar.append("█");
        }

        return bar.toString().replace("&", "§");
    }

    // Getters and Setters
    public UUID getPlayerId() {
        return playerId;
    }

    public String getQuestId() {
        return questId;
    }

    public int getCurrentProgress() {
        return currentProgress;
    }

    public boolean isCompleted() {
        return completed;
    }

    public boolean isClaimed() {
        return claimed;
    }

    public long getCompletedTime() {
        return completedTime;
    }

    public void setCompleted(boolean completed) {
        this.completed = completed;
    }

    /**
     * 设置任务为已完成状态，并确保进度正确
     */
    public void setCompleted(boolean completed, int targetAmount) {
        this.completed = completed;
        // 如果标记为完成，确保进度显示为满进度
        if (completed && currentProgress < targetAmount) {
            this.currentProgress = targetAmount;
        }
    }

    public void setClaimed(boolean claimed) {
        this.claimed = claimed;
    }

    public void setCompletedTime(long completedTime) {
        this.completedTime = completedTime;
    }
}
