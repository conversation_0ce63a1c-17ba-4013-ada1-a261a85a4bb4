package com.scavenge;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

/**
 * 撤离点监听器 - 监听撤离点物品的使用
 */
public class EvacuationListener implements Listener {

    private final ScavengePlugin plugin;

    public EvacuationListener(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (!plugin.getConfig().getBoolean("evacuation-pad.enabled", true)) {
            return;
        }

        Player player = event.getPlayer();
        ItemStack item = event.getItem();

        // 检查是否是右键点击
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        // 检查是否是撤离点物品
        if (item == null || !plugin.getEvacuationManager().isEvacuationItem(item)) {
            return;
        }

        // 检查权限
        if (!player.hasPermission("scavenge.admin")) {
            player.sendMessage("§c只有管理员可以放置撤离点！");
            event.setCancelled(true);
            return;
        }

        // 检查世界限制
        if (!plugin.getWorldRestrictionManager().isWorldAllowed(player)) {
            player.sendMessage("§c撤离点在此世界中不可用！");
            event.setCancelled(true);
            return;
        }

        Block clickedBlock = event.getClickedBlock();
        if (clickedBlock == null) {
            return;
        }

        // 获取放置位置（点击方块的上方）
        Location placeLocation = clickedBlock.getLocation().add(0, 1, 0);

        // 检查放置位置是否为空气
        if (placeLocation.getBlock().getType() != Material.AIR) {
            player.sendMessage("§c无法在此位置放置撤离点！");
            event.setCancelled(true);
            return;
        }

        // 检查是否已经是撤离点
        if (plugin.getTeleportPadManager().isTeleportPad(placeLocation)) {
            player.sendMessage("§c这个位置已经是撤离点了！");
            event.setCancelled(true);
            return;
        }

        // 检查是否有传送目标
        if (plugin.getTeleportPadManager().getSpawnLocation() == null) {
            player.sendMessage("§c传送目标未设置！请先使用 /scavenge setspawn 设置传送目标。");
            event.setCancelled(true);
            return;
        }

        // 取消事件，防止默认行为
        event.setCancelled(true);

        // 创建传送垫
        boolean success = plugin.getTeleportPadManager().createTeleportPad(placeLocation);
        if (success) {
            // 消耗物品
            if (item.getAmount() > 1) {
                item.setAmount(item.getAmount() - 1);
            } else {
                player.getInventory().setItemInHand(null);
            }

            // 发送成功消息
            String cleanName = plugin.getEvacuationManager().getCleanDisplayName(item);
            if (cleanName == null) {
                cleanName = "§6§l撤离点";
            }
            
            player.sendMessage("§a" + cleanName + " §a已成功放置在: " +
                    placeLocation.getBlockX() + ", " + placeLocation.getBlockY() + ", " + placeLocation.getBlockZ());
            player.sendMessage("§e玩家站在上面可以传送到主城！");

            // 播放放置音效
            try {
                player.playSound(player.getLocation(), org.bukkit.Sound.ANVIL_LAND, 1.0f, 1.0f);
            } catch (Exception e) {
                // 忽略音效错误
            }

        } else {
            player.sendMessage("§c创建撤离点失败！");
        }
    }
}
