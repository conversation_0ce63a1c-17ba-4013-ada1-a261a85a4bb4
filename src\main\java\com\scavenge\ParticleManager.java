package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.Effect;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 粒子效果管理器 - 管理传送垫的粒子效果
 */
public class ParticleManager {

    private final ScavengePlugin plugin;
    private final Map<String, BukkitTask> particleTasks;

    public ParticleManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.particleTasks = new ConcurrentHashMap<>();
    }

    /**
     * 为传送垫创建粒子效果
     */
    public void createParticleEffect(Location location) {
        if (!plugin.getConfig().getBoolean("teleport-pad.particles.enabled", true)) {
            return;
        }

        String locationKey = TeleportPadManager.getLocationKey(location);
        
        // 如果已经有粒子效果，先移除
        removeParticleEffect(location);

        // 获取配置
        String particleType = plugin.getConfig().getString("teleport-pad.particles.type", "VILLAGER_HAPPY");
        int count = plugin.getConfig().getInt("teleport-pad.particles.count", 3);
        double offsetX = plugin.getConfig().getDouble("teleport-pad.particles.offset.x", 0.5);
        double offsetY = plugin.getConfig().getDouble("teleport-pad.particles.offset.y", 0.1);
        double offsetZ = plugin.getConfig().getDouble("teleport-pad.particles.offset.z", 0.5);
        double speed = plugin.getConfig().getDouble("teleport-pad.particles.speed", 0.0);
        int updateInterval = plugin.getConfig().getInt("teleport-pad.particles.update-interval", 10);
        double viewDistance = plugin.getConfig().getDouble("teleport-pad.particles.view-distance", 32.0);

        // 创建粒子效果任务
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                // 检查传送垫是否还存在
                if (!plugin.getTeleportPadManager().isTeleportPad(location)) {
                    this.cancel();
                    particleTasks.remove(locationKey);
                    return;
                }

                // 检查附近是否有玩家
                boolean hasNearbyPlayer = false;
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getWorld().equals(location.getWorld()) &&
                            player.getLocation().distance(location) <= viewDistance) {
                        hasNearbyPlayer = true;
                        break;
                    }
                }

                // 如果有附近玩家，显示粒子效果
                if (hasNearbyPlayer) {
                    spawnParticles(location, particleType, count, offsetX, offsetY, offsetZ, speed);
                }
            }
        }.runTaskTimer(plugin, 0L, updateInterval);

        particleTasks.put(locationKey, task);
    }

    /**
     * 移除传送垫的粒子效果
     */
    public void removeParticleEffect(Location location) {
        String locationKey = TeleportPadManager.getLocationKey(location);
        BukkitTask task = particleTasks.remove(locationKey);
        if (task != null) {
            task.cancel();
        }
    }

    /**
     * 生成粒子效果
     */
    private void spawnParticles(Location location, String particleType, int count, 
                               double offsetX, double offsetY, double offsetZ, double speed) {
        try {
            // 计算粒子位置（传送垫上方）
            Location particleLocation = location.clone().add(0.5, 1.1, 0.5);

            // 1.8.8版本使用Effect枚举
            Effect effect = getEffectFromString(particleType);
            if (effect != null) {
                // 生成多个粒子
                for (int i = 0; i < count; i++) {
                    double x = particleLocation.getX() + (Math.random() - 0.5) * offsetX * 2;
                    double y = particleLocation.getY() + Math.random() * offsetY;
                    double z = particleLocation.getZ() + (Math.random() - 0.5) * offsetZ * 2;
                    
                    Location spawnLocation = new Location(particleLocation.getWorld(), x, y, z);
                    particleLocation.getWorld().playEffect(spawnLocation, effect, 0);
                }
            }
        } catch (Exception e) {
            // 忽略粒子效果错误
            plugin.getLogger().warning("生成粒子效果时出错: " + e.getMessage());
        }
    }

    /**
     * 从字符串获取Effect枚举（1.8.8兼容）
     */
    private Effect getEffectFromString(String particleType) {
        try {
            switch (particleType.toUpperCase()) {
                case "VILLAGER_HAPPY":
                case "HAPPY_VILLAGER":
                    return Effect.HAPPY_VILLAGER;
                case "PORTAL":
                    return Effect.ENDER_SIGNAL;
                case "ENCHANTMENT_TABLE":
                    return Effect.FLYING_GLYPH;
                case "FLAME":
                    return Effect.MOBSPAWNER_FLAMES;
                case "SMOKE":
                    return Effect.SMOKE;
                case "HEART":
                    return Effect.HEART;
                case "MAGIC_CRIT":
                    return Effect.MAGIC_CRIT;
                case "CRIT":
                    return Effect.CRIT;
                case "SPELL":
                    return Effect.POTION_SWIRL;
                case "INSTANT_SPELL":
                    return Effect.POTION_SWIRL; // 使用相同的效果
                case "WITCH_MAGIC":
                    return Effect.WITCH_MAGIC;
                case "NOTE":
                    return Effect.NOTE;
                case "SLIME":
                    return Effect.SLIME;
                case "SNOWBALL_POOF":
                    return Effect.SNOWBALL_BREAK;
                case "LARGE_SMOKE":
                    return Effect.LARGE_SMOKE;
                case "CLOUD":
                    return Effect.CLOUD;
                case "REDSTONE":
                    return Effect.COLOURED_DUST;
                case "SNOWSHOVEL":
                    return Effect.SNOW_SHOVEL;
                case "TOWN_AURA":
                    return Effect.POTION_SWIRL; // 使用药水效果替代
                case "ANGRY_VILLAGER":
                    return Effect.VILLAGER_THUNDERCLOUD;
                default:
                    return Effect.HAPPY_VILLAGER; // 默认效果
            }
        } catch (Exception e) {
            return Effect.HAPPY_VILLAGER; // 默认效果
        }
    }

    /**
     * 移除所有粒子效果
     */
    public void removeAllParticleEffects() {
        for (BukkitTask task : particleTasks.values()) {
            if (task != null) {
                task.cancel();
            }
        }
        particleTasks.clear();
    }

    /**
     * 检查位置是否有粒子效果
     */
    public boolean hasParticleEffect(Location location) {
        String locationKey = TeleportPadManager.getLocationKey(location);
        return particleTasks.containsKey(locationKey);
    }
}
