package com.scavenge;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Random;

/**
 * 淬炼石奖励类
 * 继承自ScavengeReward，专门处理淬炼石奖励
 */
public class CuilianStoneReward extends ScavengeReward {

    private final String stoneType;
    private final int level;
    private final String materialName;
    private final int minAmount;
    private final int maxAmount;
    private final Random random;

    public CuilianStoneReward(String displayName, String materialName, String stoneType, int level,
                             int minAmount, int maxAmount, double chance, int progressTime, boolean isRare) {
        // 调用父类构造函数，使用COAL作为默认材质
        super(Material.COAL, minAmount, maxAmount, displayName, null, null, chance, progressTime);

        this.stoneType = stoneType;
        this.level = level;
        this.materialName = materialName;
        this.minAmount = minAmount;
        this.maxAmount = maxAmount;
        this.random = new Random();
    }
    
    @Override
    public ItemStack createItemStack() {
        // 计算数量
        int amount = minAmount;
        if (maxAmount > minAmount) {
            amount = random.nextInt(maxAmount - minAmount + 1) + minAmount;
        }
        
        // 创建淬炼石物品
        ItemStack cuilianStone = CuilianStoneFactory.createCuilianStone(stoneType, level);
        cuilianStone.setAmount(amount);
        
        return cuilianStone;
    }
    
    @Override
    public ItemStack createDisplayItem() {
        // 创建用于GUI显示的物品
        try {
            Material displayMaterial = Material.valueOf(materialName.toUpperCase());
            ItemStack displayItem = new ItemStack(displayMaterial, 1);
            
            // 设置显示名称
            if (displayItem.getItemMeta() != null) {
                displayItem.getItemMeta().setDisplayName(getDisplayName().replace("&", "§"));
                displayItem.setItemMeta(displayItem.getItemMeta());
            }
            
            return displayItem;
        } catch (IllegalArgumentException e) {
            // 如果材质无效，使用默认的COAL
            return super.createDisplayItem();
        }
    }
    
    @Override
    public void giveReward(Player player) {
        ItemStack reward = createItemStack();
        
        // 检查背包空间
        if (player.getInventory().firstEmpty() == -1) {
            // 背包满了，掉落到地上
            player.getWorld().dropItemNaturally(player.getLocation(), reward);
            player.sendMessage("§e§l背包已满，淬炼石已掉落在地上！");
        } else {
            player.getInventory().addItem(reward);
        }
        
        // 发送获得消息
        String itemName = reward.getItemMeta().getDisplayName();
        if (itemName == null || itemName.isEmpty()) {
            itemName = "淬炼石";
        }
        
        player.sendMessage("§a你获得了 " + itemName + " §ax" + reward.getAmount() + "！");
    }
    
    /**
     * 获取淬炼石类型
     */
    public String getStoneType() {
        return stoneType;
    }
    
    /**
     * 获取淬炼石等级
     */
    public int getLevel() {
        return level;
    }
    
    /**
     * 获取显示材质名称
     */
    public String getMaterialName() {
        return materialName;
    }
}
