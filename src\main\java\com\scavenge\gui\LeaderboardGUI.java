package com.scavenge.gui;

import com.scavenge.ScavengePlugin;
import com.scavenge.leaderboard.LeaderboardManager;
import com.scavenge.leaderboard.PlayerStats;
import com.scavenge.utils.SkullUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 排行榜GUI界面
 */
public class LeaderboardGUI implements Listener {

    private final ScavengePlugin plugin;
    private final Player player;
    private Inventory inventory;
    private LeaderboardManager.LeaderboardType currentType;

    public LeaderboardGUI(ScavengePlugin plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
        this.currentType = LeaderboardManager.LeaderboardType.OVERALL_SCORE;
        createInventory();
        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 创建GUI界面
     */
    private void createInventory() {
        inventory = Bukkit.createInventory(null, 54, "§6§l✦ 搜刮排行榜 ✦ §e" + getTypeDisplayName(currentType));

        // 填充背景
        fillBackground();

        // 添加装饰边框
        addDecorations();

        // 添加排行榜类型选择按钮
        addTypeButtons();

        // 添加排行榜内容
        addLeaderboard();

        // 添加玩家自己的信息
        addPlayerInfo();

        // 添加导航按钮
        addNavigationButtons();
    }

    /**
     * 填充背景
     */
    private void fillBackground() {
        ItemStack background = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 7); // 灰色玻璃板
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);

        // 填充边框
        for (int i = 0; i < 9; i++) {
            inventory.setItem(i, background);
            inventory.setItem(45 + i, background);
        }
        for (int i = 0; i < 6; i++) {
            inventory.setItem(i * 9, background);
            inventory.setItem(i * 9 + 8, background);
        }
    }

    /**
     * 添加装饰边框
     */
    private void addDecorations() {
        // 金色玻璃装饰
        ItemStack goldGlass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 4); // 黄色玻璃板
        ItemMeta goldMeta = goldGlass.getItemMeta();
        goldMeta.setDisplayName("§6✦ 装饰 ✦");
        goldGlass.setItemMeta(goldMeta);

        // 装饰顶部和底部的特定位置
        inventory.setItem(4, goldGlass); // 顶部中央
        inventory.setItem(49, goldGlass); // 底部中央

        // 添加奖杯装饰
        ItemStack trophy = new ItemStack(Material.GOLD_BLOCK);
        ItemMeta trophyMeta = trophy.getItemMeta();
        trophyMeta.setDisplayName("§6§l🏆 排行榜 🏆");
        List<String> trophyLore = new ArrayList<>();
        trophyLore.add("§7展示服务器最强的搜刮者们!");
        trophyLore.add("§e努力搜刮，登上榜首!");
        trophyMeta.setLore(trophyLore);
        trophy.setItemMeta(trophyMeta);
        inventory.setItem(22, trophy); // 中央位置
    }

    /**
     * 添加排行榜类型选择按钮
     */
    private void addTypeButtons() {
        // 总搜刮次数
        addTypeButton(1, Material.DIAMOND_PICKAXE, LeaderboardManager.LeaderboardType.TOTAL_SCAVENGES, "总搜刮次数");

        // 完成的搜刮箱
        addTypeButton(2, Material.CHEST, LeaderboardManager.LeaderboardType.CHESTS_COMPLETED, "完成搜刮箱");

        // 稀有物品发现
        addTypeButton(3, Material.EMERALD, LeaderboardManager.LeaderboardType.RARE_ITEMS_FOUND, "稀有物品发现");

        // 任务完成
        addTypeButton(4, Material.BOOK, LeaderboardManager.LeaderboardType.QUESTS_COMPLETED, "任务完成");

        // 综合评分
        addTypeButton(5, Material.NETHER_STAR, LeaderboardManager.LeaderboardType.OVERALL_SCORE, "综合评分");
    }

    /**
     * 添加类型按钮
     */
    private void addTypeButton(int slot, Material material, LeaderboardManager.LeaderboardType type, String name) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        if (currentType == type) {
            meta.setDisplayName("§a" + name + " §7(当前)");
        } else {
            meta.setDisplayName("§7" + name);
        }

        List<String> lore = new ArrayList<>();
        lore.add("§7点击切换到此排行榜");
        meta.setLore(lore);

        button.setItemMeta(meta);
        inventory.setItem(slot, button);
    }

    /**
     * 添加排行榜内容
     */
    private void addLeaderboard() {
        List<PlayerStats> leaderboard = plugin.getLeaderboardManager().getLeaderboard(currentType, 10);

        for (int i = 0; i < Math.min(leaderboard.size(), 10); i++) {
            PlayerStats stats = leaderboard.get(i);
            ItemStack playerItem = createPlayerItem(stats, i + 1);

            // 计算位置（两列显示）
            int row = (i / 2) + 1;
            int col = (i % 2) * 3 + 2;
            int slot = row * 9 + col;

            if (slot < 45) {
                inventory.setItem(slot, playerItem);
            }
        }
    }

    /**
     * 创建玩家物品
     */
    private ItemStack createPlayerItem(PlayerStats stats, int rank) {
        // 使用SkullUtils创建带皮肤的头颅
        ItemStack item = SkullUtils.createLeaderboardSkull(stats.getPlayerName(), rank);
        SkullMeta meta = (SkullMeta) item.getItemMeta();

        if (meta != null) {
            List<String> lore = new ArrayList<>();
            lore.add("§7等级: §f" + stats.getLevel() + " (" + stats.getLevelName() + ")");
            lore.add("");

            switch (currentType) {
                case TOTAL_SCAVENGES:
                    lore.add("§7总搜刮次数: §f" + stats.getTotalScavenges());
                    break;
                case CHESTS_COMPLETED:
                    lore.add("§7完成搜刮箱: §f" + stats.getChestsCompleted());
                    break;
                case RARE_ITEMS_FOUND:
                    lore.add("§7稀有物品发现: §f" + stats.getRareItemsFound());
                    break;
                case QUESTS_COMPLETED:
                    lore.add("§7完成任务: §f" + stats.getQuestsCompleted());
                    break;
                case OVERALL_SCORE:
                    lore.add("§7综合评分: §f" + String.format("%.1f", stats.calculateScore()));
                    break;
            }

            lore.add("§7搜刮效率: §f" + String.format("%.2f", stats.getScavengeEfficiency()) + "/小时");
            lore.add("§7稀有物品率: §f" + String.format("%.2f", stats.getRareItemRate()) + "%");
            lore.add("");
            lore.add("§e点击查看详细信息!");

            meta.setLore(lore);
            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * 添加玩家自己的信息
     */
    private void addPlayerInfo() {
        PlayerStats playerStats = plugin.getLeaderboardManager().getPlayerStats(player.getUniqueId());
        if (playerStats == null)
            return;

        int playerRank = plugin.getLeaderboardManager().getPlayerRank(player.getUniqueId(), currentType);

        ItemStack playerInfo = SkullUtils.createPlayerSkull(player.getName());
        SkullMeta meta = (SkullMeta) playerInfo.getItemMeta();
        if (meta != null) {
            meta.setDisplayName("§a你的排名");
        }

        List<String> lore = new ArrayList<>();
        lore.add("§7排名: §f#" + (playerRank > 0 ? playerRank : "未上榜"));
        lore.add("§7等级: §f" + playerStats.getLevel() + " (" + playerStats.getLevelName() + ")");
        lore.add("");

        switch (currentType) {
            case TOTAL_SCAVENGES:
                lore.add("§7总搜刮次数: §f" + playerStats.getTotalScavenges());
                break;
            case CHESTS_COMPLETED:
                lore.add("§7完成搜刮箱: §f" + playerStats.getChestsCompleted());
                break;
            case RARE_ITEMS_FOUND:
                lore.add("§7稀有物品发现: §f" + playerStats.getRareItemsFound());
                break;
            case QUESTS_COMPLETED:
                lore.add("§7完成任务: §f" + playerStats.getQuestsCompleted());
                break;
            case OVERALL_SCORE:
                lore.add("§7综合评分: §f" + String.format("%.1f", playerStats.calculateScore()));
                break;
        }

        lore.add("");
        lore.add("§7下一等级需要: §f" + playerStats.getNextLevelRequirement() + " 次搜刮");

        meta.setLore(lore);
        playerInfo.setItemMeta(meta);

        inventory.setItem(49, playerInfo);
    }

    /**
     * 添加导航按钮
     */
    private void addNavigationButtons() {
        // 任务按钮
        ItemStack questButton = new ItemStack(Material.BOOK);
        ItemMeta meta = questButton.getItemMeta();
        meta.setDisplayName("§6任务");
        List<String> lore = new ArrayList<>();
        lore.add("§7查看搜刮任务");
        lore.add("§e点击打开!");
        meta.setLore(lore);
        questButton.setItemMeta(meta);
        inventory.setItem(47, questButton);

        // 关闭按钮
        ItemStack closeButton = new ItemStack(Material.BARRIER);
        meta = closeButton.getItemMeta();
        meta.setDisplayName("§c关闭");
        closeButton.setItemMeta(meta);
        inventory.setItem(53, closeButton);
    }

    /**
     * 获取类型显示名称
     */
    private String getTypeDisplayName(LeaderboardManager.LeaderboardType type) {
        switch (type) {
            case TOTAL_SCAVENGES:
                return "总搜刮次数";
            case CHESTS_COMPLETED:
                return "完成搜刮箱";
            case RARE_ITEMS_FOUND:
                return "稀有物品发现";
            case QUESTS_COMPLETED:
                return "任务完成";
            case OVERALL_SCORE:
                return "综合评分";
            default:
                return "未知";
        }
    }

    /**
     * 打开GUI
     */
    public void open() {
        player.openInventory(inventory);
    }

    /**
     * 处理点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getInventory().equals(inventory))
            return;

        event.setCancelled(true);

        if (!(event.getWhoClicked() instanceof Player))
            return;
        Player clicker = (Player) event.getWhoClicked();

        int slot = event.getSlot();

        // 处理类型切换按钮
        if (slot >= 1 && slot <= 5) {
            LeaderboardManager.LeaderboardType newType = getTypeBySlot(slot);
            if (newType != null && newType != currentType) {
                currentType = newType;
                createInventory();
                clicker.openInventory(inventory);
            }
        }
        // 处理玩家头颅点击（排行榜玩家）
        else if (isPlayerSlot(slot)) {
            PlayerStats clickedPlayer = getPlayerBySlot(slot);
            if (clickedPlayer != null) {
                showPlayerDetails(clicker, clickedPlayer);
            }
        }
        // 处理玩家自己的信息点击
        else if (slot == 49) {
            PlayerStats playerStats = plugin.getLeaderboardManager().getPlayerStats(clicker.getUniqueId());
            if (playerStats != null) {
                showPlayerDetails(clicker, playerStats);
            }
        }
        // 处理任务按钮
        else if (slot == 47) {
            clicker.closeInventory();
            QuestGUI questGUI = new QuestGUI(plugin, clicker);
            questGUI.open();
        }
        // 处理关闭按钮
        else if (slot == 53) {
            clicker.closeInventory();
        }
    }

    /**
     * 根据slot获取排行榜类型
     */
    private LeaderboardManager.LeaderboardType getTypeBySlot(int slot) {
        switch (slot) {
            case 1:
                return LeaderboardManager.LeaderboardType.TOTAL_SCAVENGES;
            case 2:
                return LeaderboardManager.LeaderboardType.CHESTS_COMPLETED;
            case 3:
                return LeaderboardManager.LeaderboardType.RARE_ITEMS_FOUND;
            case 4:
                return LeaderboardManager.LeaderboardType.QUESTS_COMPLETED;
            case 5:
                return LeaderboardManager.LeaderboardType.OVERALL_SCORE;
            default:
                return null;
        }
    }

    /**
     * 检查slot是否是玩家位置
     */
    private boolean isPlayerSlot(int slot) {
        // 根据addLeaderboard方法中的位置计算逻辑
        for (int i = 0; i < 10; i++) {
            int row = (i / 2) + 1;
            int col = (i % 2) * 3 + 2;
            int playerSlot = row * 9 + col;
            if (slot == playerSlot && playerSlot < 45) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据slot获取玩家统计数据
     */
    private PlayerStats getPlayerBySlot(int slot) {
        List<PlayerStats> leaderboard = plugin.getLeaderboardManager().getLeaderboard(currentType, 10);

        for (int i = 0; i < Math.min(leaderboard.size(), 10); i++) {
            int row = (i / 2) + 1;
            int col = (i % 2) * 3 + 2;
            int playerSlot = row * 9 + col;

            if (slot == playerSlot && playerSlot < 45) {
                return leaderboard.get(i);
            }
        }
        return null;
    }

    /**
     * 显示玩家详细信息
     */
    private void showPlayerDetails(Player viewer, PlayerStats stats) {
        viewer.sendMessage("§6§l=== " + stats.getPlayerName() + " 的详细信息 ===");
        viewer.sendMessage("§7等级: §f" + stats.getLevel() + " (" + stats.getLevelName() + ")");
        viewer.sendMessage("§7总搜刮次数: §f" + stats.getTotalScavenges());
        viewer.sendMessage("§7完成搜刮箱: §f" + stats.getChestsCompleted());
        viewer.sendMessage("§7稀有物品发现: §f" + stats.getRareItemsFound());
        viewer.sendMessage("§7完成任务: §f" + stats.getQuestsCompleted());
        viewer.sendMessage("§7搜刮效率: §f" + String.format("%.2f", stats.getScavengeEfficiency()) + "/小时");
        viewer.sendMessage("§7稀有物品率: §f" + String.format("%.2f", stats.getRareItemRate()) + "%");
        viewer.sendMessage("§7综合评分: §f" + String.format("%.1f", stats.calculateScore()));
        viewer.sendMessage("§7下一等级需要: §f" + stats.getNextLevelRequirement() + " 次搜刮");

        // 显示发现的物品统计
        if (!stats.getItemsFound().isEmpty()) {
            viewer.sendMessage("§6发现的物品:");
            for (Map.Entry<String, Integer> entry : stats.getItemsFound().entrySet()) {
                // 转换颜色代码
                String itemName = entry.getKey().replace("&", "§");
                viewer.sendMessage("§7- " + itemName + ": §f" + entry.getValue() + " 个");
            }
        }
    }
}
