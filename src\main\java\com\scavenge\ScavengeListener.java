package com.scavenge;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;

public class ScavengeListener implements Listener {

    private final ScavengePlugin plugin;

    public ScavengeListener(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItemInHand();

        // 检查是否是搜刮方块
        if (plugin.getScavengeManager().isScavengeBlock(item)) {
            // 只有管理员可以放置搜刮箱
            if (!player.hasPermission("scavenge.admin")) {
                event.setCancelled(true);
                player.sendMessage(plugin.getMessage("no-admin-permission"));
                return;
            }

            Block block = event.getBlock();
            Location location = block.getLocation();

            // 创建搜刮箱数据
            plugin.getScavengeChestManager().getOrCreateChest(location);

            player.sendMessage(plugin.getMessage("block-placed"));
        }
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction().toString().contains("RIGHT_CLICK_BLOCK")) {
            Block block = event.getClickedBlock();
            if (block != null) {
                Location location = block.getLocation();

                // 检查是否是搜刮箱
                if (plugin.getScavengeChestManager().isScavengeChest(location)) {
                    event.setCancelled(true);

                    Player player = event.getPlayer();
                    if (!player.hasPermission("scavenge.use")) {
                        player.sendMessage(plugin.getMessage("no-permission"));
                        return;
                    }

                    ScavengeChest chest = plugin.getScavengeChestManager().getChest(location);
                    if (chest == null) {
                        return;
                    }

                    // 检查世界限制
                    if (!plugin.getWorldRestrictionManager().isWorldAllowed(player)) {
                        player.sendMessage(plugin.getMessage("world-not-allowed"));
                        return;
                    }

                    // 检查是否有其他玩家正在使用
                    if (chest.isActive() && !chest.getActivePlayer().equals(player.getUniqueId())) {
                        // 箱子被其他玩家占用
                        player.sendMessage(plugin.getMessage("chest-in-use"));
                        return;
                    }

                    // 检查是否已完成或超额完成且在冷却中
                    if ((chest.isReadyForReset() || chest.shouldShowResetCountdown()) && !chest.canReset()) {
                        long remainingSeconds = chest.getRemainingCooldownSeconds();
                        String message = plugin.getConfig().getString("scavenge-chest.cooldown-message",
                                "&c这个搜刮箱还需要等待 {time} 秒才能重新搜刮!")
                                .replace("{time}", String.valueOf(remainingSeconds))
                                .replace("&", "§");
                        player.sendMessage(message);
                        return;
                    }

                    // 如果已完成且可以重置，则重置
                    if ((chest.isReadyForReset() || chest.shouldShowResetCountdown()) && chest.canReset()) {
                        chest.reset();
                        String message = plugin.getConfig().getString("scavenge-chest.available-message",
                                "&a搜刮箱已重置，可以重新搜刮了!")
                                .replace("&", "§");
                        player.sendMessage(message);
                    }

                    // 打开搜刮GUI
                    ScavengeChestGUI gui = new ScavengeChestGUI(plugin, player, chest);
                    gui.open();
                }
            }
        }
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Block block = event.getBlock();
        Location location = block.getLocation();
        Player player = event.getPlayer();

        // 检查是否是搜刮箱
        if (plugin.getScavengeChestManager().isScavengeChest(location)) {
            // 只有管理员可以破坏搜刮箱
            if (!player.hasPermission("scavenge.admin")) {
                event.setCancelled(true);
                player.sendMessage(plugin.getMessage("no-admin-permission"));
                return;
            }

            // 取消事件以防止掉落原版物品
            event.setCancelled(true);

            // 移除全息图
            plugin.getHologramManager().removeHologram(location);

            // 手动破坏方块
            block.setType(Material.AIR);

            // 掉落搜刮方块物品
            ItemStack scavengeBlock = plugin.getScavengeManager().createScavengeBlock();
            block.getWorld().dropItemNaturally(location, scavengeBlock);

            // 移除搜刮箱数据
            plugin.getScavengeChestManager().removeChest(location);

            player.sendMessage(plugin.getMessage("chest-removed"));
        }
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // 检查玩家是否正在使用搜刮箱
        for (ScavengeChest chest : plugin.getScavengeChestManager().getAllChests().values()) {
            if (chest.isActive() && chest.getActivePlayer() != null &&
                    chest.getActivePlayer().equals(player.getUniqueId())) {
                // 清除搜刮箱的活跃状态
                chest.setActive(false);
                chest.setActivePlayer(null);
                break;
            }
        }

        // 移除活跃的GUI引用
        ScavengeChestGUI.getActiveGUI(player);
    }

}
