package com.scavenge;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 撤离区域管理器
 */
public class EvacuationZoneManager {
    
    private final ScavengePlugin plugin;
    private final Map<String, EvacuationZone> zones;
    private final Map<UUID, Location> firstPoints;
    private final Map<UUID, Location> secondPoints;
    private File dataFile;
    private FileConfiguration dataConfig;
    
    public EvacuationZoneManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.zones = new ConcurrentHashMap<>();
        this.firstPoints = new ConcurrentHashMap<>();
        this.secondPoints = new ConcurrentHashMap<>();
        
        setupDataFile();
        loadData();
    }
    
    /**
     * 撤离区域类
     */
    public static class EvacuationZone {
        private final String id;
        private final Location min;
        private final Location max;
        private final World world;
        
        public EvacuationZone(String id, Location pos1, Location pos2) {
            this.id = id;
            this.world = pos1.getWorld();
            
            // 计算最小和最大坐标
            int minX = Math.min(pos1.getBlockX(), pos2.getBlockX());
            int minY = Math.min(pos1.getBlockY(), pos2.getBlockY());
            int minZ = Math.min(pos1.getBlockZ(), pos2.getBlockZ());
            
            int maxX = Math.max(pos1.getBlockX(), pos2.getBlockX());
            int maxY = Math.max(pos1.getBlockY(), pos2.getBlockY());
            int maxZ = Math.max(pos1.getBlockZ(), pos2.getBlockZ());
            
            this.min = new Location(world, minX, minY, minZ);
            this.max = new Location(world, maxX, maxY, maxZ);
        }
        
        public EvacuationZone(String id, Location min, Location max, World world) {
            this.id = id;
            this.min = min;
            this.max = max;
            this.world = world;
        }
        
        /**
         * 检查位置是否在区域内
         */
        public boolean contains(Location location) {
            if (!location.getWorld().equals(world)) {
                return false;
            }
            
            return location.getBlockX() >= min.getBlockX() && location.getBlockX() <= max.getBlockX() &&
                   location.getBlockY() >= min.getBlockY() && location.getBlockY() <= max.getBlockY() &&
                   location.getBlockZ() >= min.getBlockZ() && location.getBlockZ() <= max.getBlockZ();
        }
        
        /**
         * 获取区域中心点
         */
        public Location getCenter() {
            double centerX = (min.getX() + max.getX()) / 2.0;
            double centerY = (min.getY() + max.getY()) / 2.0;
            double centerZ = (min.getZ() + max.getZ()) / 2.0;
            return new Location(world, centerX, centerY, centerZ);
        }
        
        /**
         * 获取区域边界点列表（用于粒子效果）
         */
        public List<Location> getBorderLocations() {
            List<Location> borders = new ArrayList<>();
            
            // 底面边界
            for (int x = min.getBlockX(); x <= max.getBlockX(); x++) {
                borders.add(new Location(world, x + 0.5, min.getY(), min.getZ() + 0.5));
                borders.add(new Location(world, x + 0.5, min.getY(), max.getZ() + 0.5));
            }
            for (int z = min.getBlockZ() + 1; z < max.getBlockZ(); z++) {
                borders.add(new Location(world, min.getX() + 0.5, min.getY(), z + 0.5));
                borders.add(new Location(world, max.getX() + 0.5, min.getY(), z + 0.5));
            }
            
            // 顶面边界
            for (int x = min.getBlockX(); x <= max.getBlockX(); x++) {
                borders.add(new Location(world, x + 0.5, max.getY() + 1, min.getZ() + 0.5));
                borders.add(new Location(world, x + 0.5, max.getY() + 1, max.getZ() + 0.5));
            }
            for (int z = min.getBlockZ() + 1; z < max.getBlockZ(); z++) {
                borders.add(new Location(world, min.getX() + 0.5, max.getY() + 1, z + 0.5));
                borders.add(new Location(world, max.getX() + 0.5, max.getY() + 1, z + 0.5));
            }
            
            // 垂直边界
            for (int y = min.getBlockY() + 1; y < max.getBlockY() + 1; y++) {
                borders.add(new Location(world, min.getX() + 0.5, y, min.getZ() + 0.5));
                borders.add(new Location(world, min.getX() + 0.5, y, max.getZ() + 0.5));
                borders.add(new Location(world, max.getX() + 0.5, y, min.getZ() + 0.5));
                borders.add(new Location(world, max.getX() + 0.5, y, max.getZ() + 0.5));
            }
            
            return borders;
        }
        
        // Getters
        public String getId() { return id; }
        public Location getMin() { return min; }
        public Location getMax() { return max; }
        public World getWorld() { return world; }
    }
    
    /**
     * 设置第一个选择点
     */
    public void setFirstPoint(Player player, Location location) {
        firstPoints.put(player.getUniqueId(), location);
        player.sendMessage("§a第一个点已设置: §e" + 
            location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        
        // 如果已有第二个点，显示区域信息
        if (secondPoints.containsKey(player.getUniqueId())) {
            showZoneInfo(player);
        }
    }
    
    /**
     * 设置第二个选择点
     */
    public void setSecondPoint(Player player, Location location) {
        secondPoints.put(player.getUniqueId(), location);
        player.sendMessage("§a第二个点已设置: §e" + 
            location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        
        // 如果已有第一个点，显示区域信息
        if (firstPoints.containsKey(player.getUniqueId())) {
            showZoneInfo(player);
        }
    }
    
    /**
     * 显示区域信息
     */
    private void showZoneInfo(Player player) {
        Location pos1 = firstPoints.get(player.getUniqueId());
        Location pos2 = secondPoints.get(player.getUniqueId());
        
        if (pos1 != null && pos2 != null) {
            int volume = Math.abs(pos2.getBlockX() - pos1.getBlockX() + 1) *
                        Math.abs(pos2.getBlockY() - pos1.getBlockY() + 1) *
                        Math.abs(pos2.getBlockZ() - pos1.getBlockZ() + 1);
            
            player.sendMessage("§6撤离区域信息:");
            player.sendMessage("§7大小: §e" + volume + " 个方块");
            player.sendMessage("§7使用 §a/scavenge evacuation create <名称> §7创建撤离区域");
        }
    }
    
    /**
     * 创建撤离区域
     */
    public boolean createZone(Player player, String zoneId) {
        Location pos1 = firstPoints.get(player.getUniqueId());
        Location pos2 = secondPoints.get(player.getUniqueId());
        
        if (pos1 == null || pos2 == null) {
            player.sendMessage("§c请先选择两个点！");
            return false;
        }
        
        if (!pos1.getWorld().equals(pos2.getWorld())) {
            player.sendMessage("§c两个点必须在同一个世界！");
            return false;
        }
        
        if (zones.containsKey(zoneId)) {
            player.sendMessage("§c撤离区域 '" + zoneId + "' 已存在！");
            return false;
        }
        
        EvacuationZone zone = new EvacuationZone(zoneId, pos1, pos2);
        zones.put(zoneId, zone);
        
        // 清除选择点
        firstPoints.remove(player.getUniqueId());
        secondPoints.remove(player.getUniqueId());
        
        // 保存数据
        saveData();
        
        // 创建粒子效果
        if (plugin.getEvacuationParticleManager() != null) {
            plugin.getEvacuationParticleManager().createZoneParticles(zone);
        }
        
        player.sendMessage("§a撤离区域 '" + zoneId + "' 创建成功！");
        return true;
    }
    
    /**
     * 删除撤离区域
     */
    public boolean removeZone(String zoneId) {
        EvacuationZone zone = zones.remove(zoneId);
        if (zone != null) {
            // 移除粒子效果
            if (plugin.getEvacuationParticleManager() != null) {
                plugin.getEvacuationParticleManager().removeZoneParticles(zone);
            }
            
            saveData();
            return true;
        }
        return false;
    }
    
    /**
     * 获取玩家所在的撤离区域
     */
    public EvacuationZone getZoneAt(Location location) {
        for (EvacuationZone zone : zones.values()) {
            if (zone.contains(location)) {
                return zone;
            }
        }
        return null;
    }
    
    /**
     * 获取所有撤离区域
     */
    public Collection<EvacuationZone> getAllZones() {
        return zones.values();
    }
    
    /**
     * 获取撤离区域
     */
    public EvacuationZone getZone(String zoneId) {
        return zones.get(zoneId);
    }
    
    /**
     * 清除玩家的选择点
     */
    public void clearSelection(Player player) {
        firstPoints.remove(player.getUniqueId());
        secondPoints.remove(player.getUniqueId());
        player.sendMessage("§a选择已清除！");
    }
    
    private void setupDataFile() {
        dataFile = new File(plugin.getDataFolder(), "evacuation_zones.yml");
        if (!dataFile.exists()) {
            try {
                dataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建撤离区域数据文件: " + e.getMessage());
            }
        }
        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
    }
    
    private void saveData() {
        try {
            // 清除现有数据
            dataConfig.set("zones", null);
            
            // 保存所有区域
            for (EvacuationZone zone : zones.values()) {
                String path = "zones." + zone.getId();
                dataConfig.set(path + ".world", zone.getWorld().getName());
                dataConfig.set(path + ".min.x", zone.getMin().getBlockX());
                dataConfig.set(path + ".min.y", zone.getMin().getBlockY());
                dataConfig.set(path + ".min.z", zone.getMin().getBlockZ());
                dataConfig.set(path + ".max.x", zone.getMax().getBlockX());
                dataConfig.set(path + ".max.y", zone.getMax().getBlockY());
                dataConfig.set(path + ".max.z", zone.getMax().getBlockZ());
            }
            
            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存撤离区域数据: " + e.getMessage());
        }
    }
    
    private void loadData() {
        ConfigurationSection zonesSection = dataConfig.getConfigurationSection("zones");
        if (zonesSection == null) {
            return;
        }
        
        for (String zoneId : zonesSection.getKeys(false)) {
            try {
                String worldName = zonesSection.getString(zoneId + ".world");
                World world = plugin.getServer().getWorld(worldName);
                
                if (world == null) {
                    plugin.getLogger().warning("撤离区域 '" + zoneId + "' 的世界 '" + worldName + "' 不存在，跳过加载");
                    continue;
                }
                
                int minX = zonesSection.getInt(zoneId + ".min.x");
                int minY = zonesSection.getInt(zoneId + ".min.y");
                int minZ = zonesSection.getInt(zoneId + ".min.z");
                int maxX = zonesSection.getInt(zoneId + ".max.x");
                int maxY = zonesSection.getInt(zoneId + ".max.y");
                int maxZ = zonesSection.getInt(zoneId + ".max.z");
                
                Location min = new Location(world, minX, minY, minZ);
                Location max = new Location(world, maxX, maxY, maxZ);
                
                EvacuationZone zone = new EvacuationZone(zoneId, min, max, world);
                zones.put(zoneId, zone);
                
                plugin.getLogger().info("加载撤离区域: " + zoneId);
                
            } catch (Exception e) {
                plugin.getLogger().warning("加载撤离区域 '" + zoneId + "' 时出错: " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("共加载了 " + zones.size() + " 个撤离区域");
    }
}
