package com.scavenge;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Random;

/**
 * 宝石奖励类
 * 继承自ScavengeReward，专门处理强化宝石奖励
 */
public class GemStoneReward extends ScavengeReward {
    
    private final String gemType;
    private final int level;
    private final int chanceValue;
    private final String materialName;
    private final int minAmount;
    private final int maxAmount;
    private final Random random;
    
    public GemStoneReward(String displayName, String materialName, String gemType, int level, int chanceValue,
                         int minAmount, int maxAmount, double chance, int progressTime, boolean isRare) {
        // 调用父类构造函数，使用WOOD作为默认材质
        super(Material.WOOD, minAmount, maxAmount, displayName, null, null, chance, progressTime);
        
        this.gemType = gemType;
        this.level = level;
        this.chanceValue = chanceValue;
        this.materialName = materialName;
        this.minAmount = minAmount;
        this.maxAmount = maxAmount;
        this.random = new Random();
    }
    
    @Override
    public ItemStack createItemStack() {
        // 计算数量
        int amount = minAmount;
        if (maxAmount > minAmount) {
            amount = random.nextInt(maxAmount - minAmount + 1) + minAmount;
        }
        
        // 创建宝石物品
        ItemStack gemStone = GemStoneFactory.createGemStone(gemType, level, chanceValue);
        gemStone.setAmount(amount);
        
        return gemStone;
    }
    
    @Override
    public ItemStack createDisplayItem() {
        // 创建用于GUI显示的物品
        try {
            Material displayMaterial = Material.valueOf(materialName.toUpperCase());
            ItemStack displayItem = new ItemStack(displayMaterial, 1);
            
            // 设置显示名称
            if (displayItem.getItemMeta() != null) {
                displayItem.getItemMeta().setDisplayName(getDisplayName().replace("&", "§"));
                displayItem.setItemMeta(displayItem.getItemMeta());
            }
            
            return displayItem;
        } catch (IllegalArgumentException e) {
            // 如果材质无效，使用默认的WOOD
            return super.createDisplayItem();
        }
    }
    
    @Override
    public void giveReward(Player player) {
        ItemStack reward = createItemStack();
        
        // 检查背包空间
        if (player.getInventory().firstEmpty() == -1) {
            // 背包满了，掉落到地上
            player.getWorld().dropItemNaturally(player.getLocation(), reward);
            player.sendMessage("§e§l背包已满，强化宝石已掉落在地上！");
        } else {
            player.getInventory().addItem(reward);
        }
        
        // 发送获得消息
        String itemName = reward.getItemMeta().getDisplayName();
        if (itemName == null || itemName.isEmpty()) {
            itemName = "强化宝石";
        }
        
        player.sendMessage("§a你获得了 " + itemName + " §ax" + reward.getAmount() + "！");
    }
    
    /**
     * 获取宝石类型
     */
    public String getGemType() {
        return gemType;
    }
    
    /**
     * 获取宝石等级
     */
    public int getLevel() {
        return level;
    }
    
    /**
     * 获取成功几率
     */
    public int getChanceValue() {
        return chanceValue;
    }
    
    /**
     * 获取显示材质名称
     */
    public String getMaterialName() {
        return materialName;
    }
}
