package com.scavenge;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 撤离区域倒计时管理器
 */
public class EvacuationCountdownManager {
    
    private final ScavengePlugin plugin;
    private final Map<UUID, CountdownTask> activeCountdowns;
    
    public EvacuationCountdownManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.activeCountdowns = new ConcurrentHashMap<>();
    }
    
    /**
     * 开始传送倒计时
     */
    public boolean startCountdown(Player player, EvacuationZoneManager.EvacuationZone zone) {
        plugin.getLogger().info("[调试] EvacuationCountdownManager.startCountdown 被调用");
        
        boolean enabled = plugin.getConfig().getBoolean("evacuation-zone.enabled", true);
        plugin.getLogger().info("[调试] 撤离区域功能启用状态: " + enabled);
        
        if (!enabled) {
            return false;
        }
        
        // 检查是否已有倒计时
        boolean hasActive = activeCountdowns.containsKey(player.getUniqueId());
        plugin.getLogger().info("[调试] 玩家是否已有活跃倒计时: " + hasActive);
        
        if (hasActive) {
            return false;
        }
        
        // 检查是否有传送目标
        Location spawnLocation = plugin.getTeleportPadManager().getSpawnLocation();
        plugin.getLogger().info("[调试] 获取到的传送目标: " + (spawnLocation != null ? 
            spawnLocation.getWorld().getName() + " " + spawnLocation.getBlockX() + "," + 
            spawnLocation.getBlockY() + "," + spawnLocation.getBlockZ() : "null"));
        
        if (spawnLocation == null) {
            player.sendMessage("§c传送目标未设置！请联系管理员。");
            return false;
        }
        
        plugin.getLogger().info("[调试] 创建倒计时任务");
        
        // 创建倒计时任务
        CountdownTask task = new CountdownTask(player, zone, spawnLocation);
        activeCountdowns.put(player.getUniqueId(), task);
        
        plugin.getLogger().info("[调试] 启动倒计时任务");
        
        // 开始倒计时
        task.start();
        
        plugin.getLogger().info("[调试] 倒计时任务启动完成");
        
        return true;
    }
    
    /**
     * 取消传送倒计时
     */
    public void cancelCountdown(Player player) {
        CountdownTask task = activeCountdowns.remove(player.getUniqueId());
        if (task != null) {
            task.cancel();
        }
    }
    
    /**
     * 检查玩家是否有活跃的倒计时
     */
    public boolean hasActiveCountdown(Player player) {
        return activeCountdowns.containsKey(player.getUniqueId());
    }
    
    /**
     * 清除所有倒计时
     */
    public void clearAllCountdowns() {
        for (CountdownTask task : activeCountdowns.values()) {
            task.cancel();
        }
        activeCountdowns.clear();
    }
    
    /**
     * 倒计时任务类
     */
    private class CountdownTask {
        private final Player player;
        private final EvacuationZoneManager.EvacuationZone zone;
        private final Location targetLocation;
        private final int countdownTime;
        private int remainingTime;
        private BukkitTask task;
        
        public CountdownTask(Player player, EvacuationZoneManager.EvacuationZone zone, Location targetLocation) {
            this.player = player;
            this.zone = zone;
            this.targetLocation = targetLocation;
            this.countdownTime = plugin.getConfig().getInt("evacuation-zone.countdown-time", 5);
            this.remainingTime = countdownTime;
        }
        
        public void start() {
            plugin.getLogger().info("[调试] CountdownTask.start() 被调用，倒计时时间: " + countdownTime + " 秒");
            
            // 发送开始消息
            boolean chatEnabled = plugin.getConfig().getBoolean("evacuation-zone.countdown-display.chat.enabled", true);
            plugin.getLogger().info("[调试] 聊天消息启用状态: " + chatEnabled);
            
            if (chatEnabled) {
                String startMessage = plugin.getConfig().getString("evacuation-zone.countdown-display.chat.start-message",
                    "&a进入撤离区域，开始传送倒计时...");
                plugin.getLogger().info("[调试] 发送开始消息: " + startMessage);
                player.sendMessage(startMessage.replace("&", "§"));
            }
            
            // 播放开始音效
            playSound("start");
            
            plugin.getLogger().info("[调试] 创建BukkitRunnable任务");
            
            // 开始倒计时任务
            task = new BukkitRunnable() {
                @Override
                public void run() {
                    plugin.getLogger().info("[调试] 倒计时任务执行，剩余时间: " + remainingTime);
                    
                    if (!player.isOnline()) {
                        plugin.getLogger().info("[调试] 玩家不在线，取消倒计时");
                        cancel();
                        return;
                    }
                    
                    // 检查玩家是否还在撤离区域内
                    boolean inZone = zone.contains(player.getLocation());
                    plugin.getLogger().info("[调试] 玩家是否在撤离区域内: " + inZone);
                    
                    if (!inZone) {
                        plugin.getLogger().info("[调试] 玩家离开撤离区域，取消倒计时");
                        cancelWithMessage();
                        return;
                    }
                    
                    plugin.getLogger().info("[调试] 所有检查通过，更新显示");
                    
                    // 更新显示
                    updateDisplay();
                    
                    // 播放倒计时音效
                    if (remainingTime > 0) {
                        playSound("tick");
                    }
                    
                    // 检查倒计时是否结束
                    if (remainingTime <= 0) {
                        plugin.getLogger().info("[调试] 倒计时结束，执行传送");
                        // 执行传送
                        executeTeleport();
                        cancel();
                        return;
                    }
                    
                    remainingTime--;
                    plugin.getLogger().info("[调试] 倒计时任务完成，下次剩余时间: " + remainingTime);
                }
            }.runTaskTimer(plugin, 0L, 20L); // 每秒执行一次
        }
        
        public void cancel() {
            if (task != null) {
                task.cancel();
            }
            activeCountdowns.remove(player.getUniqueId());
        }
        
        private void updateDisplay() {
            // 显示标题
            if (plugin.getConfig().getBoolean("evacuation-zone.countdown-display.title.enabled", true)) {
                String titleFormat = plugin.getConfig().getString("evacuation-zone.countdown-display.title.format",
                    "&6&l撤离倒计时");
                String title = titleFormat.replace("&", "§");
                
                String subtitleFormat = plugin.getConfig().getString("evacuation-zone.countdown-display.subtitle.format",
                    "&e{time} 秒后传送到安全区域");
                String subtitle = subtitleFormat.replace("{time}", String.valueOf(remainingTime)).replace("&", "§");
                
                // 使用反射发送标题（兼容1.8.8）
                sendTitle(player, title, subtitle);
            }
            
            // 同时发送聊天消息作为备用
            String chatMessage = "§6撤离倒计时: §e" + remainingTime + " 秒";
            player.sendMessage(chatMessage);
        }
        
        private void cancelWithMessage() {
            plugin.getLogger().info("[调试] 倒计时被取消，发送取消消息");
            
            // 发送取消消息
            if (plugin.getConfig().getBoolean("evacuation-zone.countdown-display.chat.enabled", true)) {
                String cancelMessage = plugin.getConfig().getString("evacuation-zone.countdown-display.chat.cancel-message",
                    "&c离开撤离区域，传送已取消！");
                player.sendMessage(cancelMessage.replace("&", "§"));
            }
            
            // 播放取消音效
            playSound("cancel");
            
            // 清除标题
            sendTitle(player, "", "");
            
            cancel();
        }
        
        private void executeTeleport() {
            // 传送玩家
            player.teleport(targetLocation);
            
            // 发送成功消息
            if (plugin.getConfig().getBoolean("evacuation-zone.countdown-display.chat.enabled", true)) {
                String successMessage = plugin.getConfig().getString("evacuation-zone.countdown-display.chat.success-message",
                    "&a成功撤离到安全区域！");
                player.sendMessage(successMessage.replace("&", "§"));
            }
            
            // 播放成功音效
            playSound("success");
            
            // 清除标题
            sendTitle(player, "", "");
        }
        
        private void playSound(String soundType) {
            try {
                String soundName = plugin.getConfig().getString("evacuation-zone.sounds." + soundType + ".sound", "");
                if (!soundName.isEmpty()) {
                    Sound sound = Sound.valueOf(soundName);
                    float volume = (float) plugin.getConfig().getDouble("evacuation-zone.sounds." + soundType + ".volume", 1.0);
                    float pitch = (float) plugin.getConfig().getDouble("evacuation-zone.sounds." + soundType + ".pitch", 1.0);
                    player.playSound(player.getLocation(), sound, volume, pitch);
                }
            } catch (Exception e) {
                // 忽略音效错误
            }
        }
        
        private void sendTitle(Player player, String title, String subtitle) {
            try {
                // 尝试使用简单的方法（如果服务器支持）
                if (hasSimpleTitleMethod()) {
                    player.sendTitle(title, subtitle);
                    return;
                }
                
                // 1.8.8版本使用反射发送标题
                String version = plugin.getServer().getClass().getPackage().getName().split("\\.")[3];
                
                Class<?> craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + version + ".entity.CraftPlayer");
                Object craftPlayer = craftPlayerClass.cast(player);
                Object handle = craftPlayerClass.getMethod("getHandle").invoke(craftPlayer);
                Object connection = handle.getClass().getField("playerConnection").get(handle);
                
                Class<?> packetClass = Class.forName("net.minecraft.server." + version + ".PacketPlayOutTitle");
                Class<?> chatComponentText = Class.forName("net.minecraft.server." + version + ".ChatComponentText");
                Class<?> enumTitleAction = Class.forName("net.minecraft.server." + version + ".PacketPlayOutTitle$EnumTitleAction");
                
                Object titleAction = enumTitleAction.getField("TITLE").get(null);
                Object subtitleAction = enumTitleAction.getField("SUBTITLE").get(null);
                
                if (!title.isEmpty()) {
                    Object titleComponent = chatComponentText.getConstructor(String.class).newInstance(title);
                    Object titlePacket = packetClass.getConstructor(enumTitleAction, chatComponentText).newInstance(titleAction, titleComponent);
                    connection.getClass().getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet")).invoke(connection, titlePacket);
                }
                
                if (!subtitle.isEmpty()) {
                    Object subtitleComponent = chatComponentText.getConstructor(String.class).newInstance(subtitle);
                    Object subtitlePacket = packetClass.getConstructor(enumTitleAction, chatComponentText).newInstance(subtitleAction, subtitleComponent);
                    connection.getClass().getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet")).invoke(connection, subtitlePacket);
                }
                
            } catch (Exception e) {
                // 如果反射失败，发送聊天消息作为替代
                if (!title.isEmpty() || !subtitle.isEmpty()) {
                    String message = title;
                    if (!subtitle.isEmpty()) {
                        message += (message.isEmpty() ? "" : " ") + subtitle;
                    }
                    if (!message.isEmpty()) {
                        player.sendMessage("§6§l[标题] " + message);
                    }
                }
            }
        }
        
        private boolean hasSimpleTitleMethod() {
            try {
                Player.class.getMethod("sendTitle", String.class, String.class);
                return true;
            } catch (NoSuchMethodException e) {
                return false;
            }
        }
    }
}
