package com.scavenge;

import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.Location;

import java.util.List;

/**
 * 撤离区域选择监听器
 */
public class EvacuationZoneListener implements Listener {
    
    private final ScavengePlugin plugin;
    
    public EvacuationZoneListener(ScavengePlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        // 检查是否是选择工具
        if (!isSelectionTool(item)) {
            return;
        }
        
        // 检查权限
        if (!player.hasPermission("scavenge.admin") && !player.isOp()) {
            player.sendMessage("§c你没有权限使用撤离区域选择工具！");
            event.setCancelled(true);
            return;
        }
        
        Block clickedBlock = event.getClickedBlock();
        if (clickedBlock == null) {
            return;
        }
        
        Location location = clickedBlock.getLocation();
        
        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // 左键设置第一个点
            plugin.getEvacuationZoneManager().setFirstPoint(player, location);
            
            // 显示选择粒子效果
            if (plugin.getEvacuationParticleManager() != null) {
                plugin.getEvacuationParticleManager().showSelectionParticles(player, location);
            }
            
            event.setCancelled(true);
            
        } else if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // 右键设置第二个点
            plugin.getEvacuationZoneManager().setSecondPoint(player, location);
            
            // 显示选择粒子效果
            if (plugin.getEvacuationParticleManager() != null) {
                plugin.getEvacuationParticleManager().showSelectionParticles(player, location);
            }
            
            event.setCancelled(true);
        }
    }
    
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        if (!plugin.getConfig().getBoolean("evacuation-zone.enabled", true)) {
            return;
        }
        
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();
        
        // 检查玩家是否移动到了新的方块
        if (from.getBlockX() == to.getBlockX() && 
            from.getBlockY() == to.getBlockY() && 
            from.getBlockZ() == to.getBlockZ()) {
            return;
        }
        
        // 检查玩家是否进入或离开撤离区域
        EvacuationZoneManager.EvacuationZone fromZone = plugin.getEvacuationZoneManager().getZoneAt(from);
        EvacuationZoneManager.EvacuationZone toZone = plugin.getEvacuationZoneManager().getZoneAt(to);
        
        if (fromZone != toZone) {
            if (toZone != null) {
                // 玩家进入撤离区域
                handlePlayerEnterZone(player, toZone);
            } else if (fromZone != null) {
                // 玩家离开撤离区域
                handlePlayerLeaveZone(player, fromZone);
            }
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // 玩家退出时取消倒计时
        plugin.getEvacuationCountdownManager().cancelCountdown(event.getPlayer());
    }
    
    /**
     * 检查物品是否是选择工具
     */
    private boolean isSelectionTool(ItemStack item) {
        if (item == null || item.getType() != Material.GOLD_AXE) { // 1.8.8兼容
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return false;
        }
        
        String expectedName = plugin.getConfig().getString("evacuation-zone.selection-tool.display-name", "&6&l撤离区域选择工具");
        return meta.getDisplayName().equals(expectedName.replace("&", "§"));
    }
    
    /**
     * 处理玩家进入撤离区域
     */
    private void handlePlayerEnterZone(Player player, EvacuationZoneManager.EvacuationZone zone) {
        plugin.getLogger().info("[调试] 玩家 " + player.getName() + " 进入撤离区域: " + zone.getId());
        
        // 检查玩家是否已经在倒计时中
        if (plugin.getEvacuationCountdownManager().hasActiveCountdown(player)) {
            plugin.getLogger().info("[调试] 玩家已在倒计时中，跳过");
            return;
        }
        
        // 检查权限 - 管理员和OP可以绕过所有限制
        boolean isAdmin = player.hasPermission("scavenge.admin") || player.isOp();
        boolean isDenied = player.hasPermission("scavenge.evacuation.deny");

        plugin.getLogger().info("[调试] 玩家 " + player.getName() + " 权限检查: admin=" + isAdmin + ", denied=" + isDenied);

        if (!isAdmin && isDenied) {
            player.sendMessage("§c你没有权限使用撤离区域！");
            return;
        }

        // 检查世界限制（管理员可以绕过）
        if (!isAdmin && !plugin.getWorldRestrictionManager().isWorldAllowed(player)) {
            player.sendMessage("§c撤离区域在此世界中不可用！");
            return;
        }
        
        // 检查传送目标
        Location spawnLocation = plugin.getTeleportPadManager().getSpawnLocation();
        if (spawnLocation == null) {
            player.sendMessage("§c传送目标未设置！请联系管理员。");
            return;
        }
        
        // 开始倒计时
        boolean success = plugin.getEvacuationCountdownManager().startCountdown(player, zone);
        if (!success) {
            player.sendMessage("§c无法开始传送倒计时！");
        }
    }
    
    /**
     * 处理玩家离开撤离区域
     */
    private void handlePlayerLeaveZone(Player player, EvacuationZoneManager.EvacuationZone zone) {
        plugin.getLogger().info("[调试] 玩家 " + player.getName() + " 离开撤离区域: " + zone.getId());
        
        // 取消倒计时
        if (plugin.getEvacuationCountdownManager().hasActiveCountdown(player)) {
            plugin.getEvacuationCountdownManager().cancelCountdown(player);
        }
    }
    
    /**
     * 创建选择工具
     */
    public static ItemStack createSelectionTool(ScavengePlugin plugin) {
        String materialName = plugin.getConfig().getString("evacuation-zone.selection-tool.material", "GOLD_AXE");
        Material material;

        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的选择工具材质: " + materialName + "，使用默认材质");
            material = Material.GOLD_AXE; // 1.8.8兼容
        }
        
        ItemStack tool = new ItemStack(material);
        ItemMeta meta = tool.getItemMeta();
        
        if (meta != null) {
            // 设置显示名称
            String displayName = plugin.getConfig().getString("evacuation-zone.selection-tool.display-name", "&6&l撤离区域选择工具");
            meta.setDisplayName(displayName.replace("&", "§"));
            
            // 设置描述
            List<String> loreConfig = plugin.getConfig().getStringList("evacuation-zone.selection-tool.lore");
            if (loreConfig.isEmpty()) {
                loreConfig.add("&7左键点击设置第一个点");
                loreConfig.add("&7右键点击设置第二个点");
                loreConfig.add("&7选择完成后使用指令创建撤离区域");
                loreConfig.add("&e&l管理员专用工具");
            }
            
            List<String> lore = new java.util.ArrayList<>();
            for (String line : loreConfig) {
                lore.add(line.replace("&", "§"));
            }
            meta.setLore(lore);
            
            // 添加发光效果
            if (plugin.getConfig().getBoolean("evacuation-zone.selection-tool.enchanted", true)) {
                meta.addEnchant(org.bukkit.enchantments.Enchantment.DURABILITY, 1, true);
                meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);
            }
            
            tool.setItemMeta(meta);
        }
        
        return tool;
    }
}
