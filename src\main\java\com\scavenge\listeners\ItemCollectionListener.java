package com.scavenge.listeners;

import com.scavenge.ScavengePlugin;
import org.bukkit.event.Listener;

/**
 * 物品收集监听器 - 已禁用，防止玩家通过丢弃物品作弊
 * 现在只有从搜刮箱中获得的物品才会计入任务进度
 */
public class ItemCollectionListener implements Listener {

    private final ScavengePlugin plugin;

    public ItemCollectionListener(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    // 所有监听器方法已移除，防止玩家通过丢弃物品作弊
    // 物品收集任务进度现在只在搜刮箱中直接更新
}
