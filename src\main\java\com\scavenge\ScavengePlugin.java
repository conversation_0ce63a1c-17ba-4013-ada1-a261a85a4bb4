package com.scavenge;

import com.scavenge.quest.QuestManager;
import com.scavenge.leaderboard.LeaderboardManager;
import com.scavenge.level.LevelManager;
import com.scavenge.listeners.ItemCollectionListener;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.Bukkit;

public class ScavengePlugin extends JavaPlugin {

    private static ScavengePlugin instance;
    private FileConfiguration config;
    private ScavengeManager scavengeManager;
    private ScavengeChestManager scavengeChestManager;
    private HologramManager hologramManager;
    private QuestManager questManager;
    private LeaderboardManager leaderboardManager;
    private LevelManager levelManager;
    private WorldRestrictionManager worldRestrictionManager;
    private TeleportPadManager teleportPadManager;
    private CraftingManager craftingManager;

    // 新的撤离区域系统
    private EvacuationZoneManager evacuationZoneManager;
    private EvacuationParticleManager evacuationParticleManager;
    private EvacuationCountdownManager evacuationCountdownManager;

    @Override
    public void onEnable() {
        instance = this;

        // 保存默认配置文件
        saveDefaultConfig();
        config = getConfig();

        // 初始化管理器
        scavengeManager = new ScavengeManager(this);
        scavengeChestManager = new ScavengeChestManager(this);
        hologramManager = new HologramManager(this);
        questManager = new QuestManager(this);
        leaderboardManager = new LeaderboardManager(this);
        levelManager = new LevelManager(this);
        worldRestrictionManager = new WorldRestrictionManager(this);
        teleportPadManager = new TeleportPadManager(this);
        craftingManager = new CraftingManager(this);

        // 初始化新的撤离区域系统
        evacuationZoneManager = new EvacuationZoneManager(this);
        evacuationParticleManager = new EvacuationParticleManager(this);
        evacuationCountdownManager = new EvacuationCountdownManager(this);

        // 延迟初始化粒子效果（确保所有管理器都已初始化）
        Bukkit.getScheduler().runTaskLater(this, new Runnable() {
            @Override
            public void run() {
                ScavengePlugin.this.initializeEvacuationParticles();
            }
        }, 20L); // 延迟1秒

        // 注册命令和Tab补全
        getCommand("scavenge").setExecutor(new ScavengeCommand(this));
        this.getCommand("scavenge").setTabCompleter(new ScavengeTabCompleter(this));

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(new ScavengeListener(this), this);
        Bukkit.getPluginManager().registerEvents(new ItemCollectionListener(this), this);
        Bukkit.getPluginManager().registerEvents(worldRestrictionManager, this);
        Bukkit.getPluginManager().registerEvents(new CraftingListener(), this);

        // 注册新的撤离区域监听器
        Bukkit.getPluginManager().registerEvents(new EvacuationZoneListener(this), this);

        // 注册合成配方
        craftingManager.registerAllRecipes();

        getLogger().info("搜刮插件已启用!");
    }

    @Override
    public void onDisable() {
        // 清理全息图
        if (hologramManager != null) {
            hologramManager.removeAllHolograms();
        }

        // 关闭搜刮箱管理器
        if (scavengeChestManager != null) {
            scavengeChestManager.shutdown();
        }

        // 保存任务数据
        if (questManager != null) {
            questManager.shutdown();
        }

        // 保存排行榜数据
        if (leaderboardManager != null) {
            leaderboardManager.shutdown();
        }

        // 关闭传送垫管理器
        if (teleportPadManager != null) {
            teleportPadManager.shutdown();
        }

        // 移除合成配方
        if (craftingManager != null) {
            craftingManager.unregisterAllRecipes();
        }




        // 关闭新的撤离区域系统
        if (evacuationCountdownManager != null) {
            evacuationCountdownManager.clearAllCountdowns();
        }

        if (evacuationParticleManager != null) {
            evacuationParticleManager.removeAllParticles();
        }

        getLogger().info("搜刮插件已禁用!");
    }

    public static ScavengePlugin getInstance() {
        return instance;
    }

    public ScavengeManager getScavengeManager() {
        return scavengeManager;
    }

    public ScavengeChestManager getScavengeChestManager() {
        return scavengeChestManager;
    }

    public HologramManager getHologramManager() {
        return hologramManager;
    }

    public QuestManager getQuestManager() {
        return questManager;
    }

    public LeaderboardManager getLeaderboardManager() {
        return leaderboardManager;
    }

    public LevelManager getLevelManager() {
        return levelManager;
    }

    public WorldRestrictionManager getWorldRestrictionManager() {
        return worldRestrictionManager;
    }

    /**
     * 重新加载插件配置
     */
    public void reloadPluginConfig() {
        try {
            // 重新加载配置文件
            reloadConfig();
            config = super.getConfig();

            // 重新加载搜刮管理器的配置（奖励配置等）
            if (scavengeManager != null) {
                scavengeManager.reloadConfig();
            }

            // 重新加载搜刮箱管理器的配置（重置时间等）
            if (scavengeChestManager != null) {
                scavengeChestManager.reloadConfig();
            }

            getLogger().info("配置文件已成功重新加载!");
        } catch (Exception e) {
            getLogger().severe("重新加载配置时出错: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public FileConfiguration getConfig() {
        if (config == null) {
            config = super.getConfig();
        }
        return config;
    }

    public String getMessage(String key) {
        return getConfig().getString("messages." + key, "&c消息未找到: " + key)
                .replace("&", "§");
    }

    public String getMessage(String key, String placeholder, String value) {
        return getMessage(key).replace("{" + placeholder + "}", value);
    }

    public TeleportPadManager getTeleportPadManager() {
        return teleportPadManager;
    }
    public CraftingManager getCraftingManager() {
        return craftingManager;
    }





    // 新的撤离区域系统的getter方法
    public EvacuationZoneManager getEvacuationZoneManager() {
        return evacuationZoneManager;
    }

    public EvacuationParticleManager getEvacuationParticleManager() {
        return evacuationParticleManager;
    }

    public EvacuationCountdownManager getEvacuationCountdownManager() {
        return evacuationCountdownManager;
    }

    /**
     * 初始化撤离区域粒子效果
     */
    private void initializeEvacuationParticles() {
        if (evacuationZoneManager != null && evacuationParticleManager != null) {
            for (EvacuationZoneManager.EvacuationZone zone : evacuationZoneManager.getAllZones()) {
                evacuationParticleManager.createZoneParticles(zone);
            }
            getLogger().info("服务器启动后为 " + evacuationZoneManager.getAllZones().size() + " 个撤离区域初始化了粒子效果");
        }
    }
}
