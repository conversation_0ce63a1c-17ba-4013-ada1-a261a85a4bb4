package com.scavenge.level;

import com.scavenge.ScavengePlugin;
import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 等级系统管理器
 */
public class LevelManager {
    
    private final ScavengePlugin plugin;
    private File levelFile;
    private FileConfiguration levelConfig;
    private final Map<Integer, LevelInfo> levels;
    private final Map<Integer, List<String>> levelRewards;
    
    public LevelManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.levels = new TreeMap<>();
        this.levelRewards = new HashMap<>();
        
        setupLevelFile();
        loadLevels();
        loadLevelRewards();
    }
    
    /**
     * 设置等级配置文件
     */
    private void setupLevelFile() {
        levelFile = new File(plugin.getDataFolder(), "levels.yml");
        if (!levelFile.exists()) {
            plugin.saveResource("levels.yml", false);
        }
        levelConfig = YamlConfiguration.loadConfiguration(levelFile);
    }
    
    /**
     * 加载等级配置
     */
    private void loadLevels() {
        levels.clear();
        
        ConfigurationSection levelsSection = levelConfig.getConfigurationSection("levels");
        if (levelsSection == null) {
            plugin.getLogger().warning("等级配置文件中未找到levels配置！");
            return;
        }
        
        for (String levelStr : levelsSection.getKeys(false)) {
            try {
                int level = Integer.parseInt(levelStr);
                ConfigurationSection levelSection = levelsSection.getConfigurationSection(levelStr);
                
                String name = levelSection.getString("name", "未知等级");
                int requirement = levelSection.getInt("requirement", 0);
                String description = levelSection.getString("description", "");
                
                LevelInfo levelInfo = new LevelInfo(level, name, requirement, description);
                levels.put(level, levelInfo);
                
                plugin.getLogger().info("加载等级: " + level + " - " + name + " (需要: " + requirement + ")");
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("无效的等级配置: " + levelStr);
            }
        }
        
        plugin.getLogger().info("总共加载了 " + levels.size() + " 个等级");
    }
    
    /**
     * 加载等级奖励配置
     */
    private void loadLevelRewards() {
        levelRewards.clear();
        
        ConfigurationSection rewardsSection = levelConfig.getConfigurationSection("level-rewards");
        if (rewardsSection == null) {
            return;
        }
        
        for (String levelStr : rewardsSection.getKeys(false)) {
            try {
                int level = Integer.parseInt(levelStr);
                List<String> rewards = rewardsSection.getStringList(levelStr);
                levelRewards.put(level, rewards);
                
                plugin.getLogger().info("加载等级 " + level + " 的奖励: " + rewards.size() + " 个");
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("无效的等级奖励配置: " + levelStr);
            }
        }
    }
    
    /**
     * 根据搜刮次数获取等级
     */
    public int getLevel(int scavengeCount) {
        int currentLevel = 1;
        
        for (LevelInfo levelInfo : levels.values()) {
            if (scavengeCount >= levelInfo.getRequirement()) {
                currentLevel = levelInfo.getLevel();
            } else {
                break;
            }
        }
        
        return currentLevel;
    }
    
    /**
     * 根据搜刮次数获取等级名称
     */
    public String getLevelName(int scavengeCount) {
        int level = getLevel(scavengeCount);
        LevelInfo levelInfo = levels.get(level);
        return levelInfo != null ? levelInfo.getName() : "未知等级";
    }
    
    /**
     * 获取等级信息
     */
    public LevelInfo getLevelInfo(int level) {
        return levels.get(level);
    }
    
    /**
     * 获取下一等级所需的搜刮次数
     */
    public int getNextLevelRequirement(int currentScavengeCount) {
        int currentLevel = getLevel(currentScavengeCount);
        
        // 查找下一个等级
        for (LevelInfo levelInfo : levels.values()) {
            if (levelInfo.getLevel() > currentLevel) {
                return levelInfo.getRequirement() - currentScavengeCount;
            }
        }
        
        // 已达到最高等级
        return 0;
    }
    
    /**
     * 获取下一等级信息
     */
    public LevelInfo getNextLevelInfo(int currentScavengeCount) {
        int currentLevel = getLevel(currentScavengeCount);
        
        for (LevelInfo levelInfo : levels.values()) {
            if (levelInfo.getLevel() > currentLevel) {
                return levelInfo;
            }
        }
        
        return null; // 已达到最高等级
    }
    
    /**
     * 检查是否升级并处理升级逻辑
     */
    public boolean checkLevelUp(Player player, int oldScavengeCount, int newScavengeCount) {
        int oldLevel = getLevel(oldScavengeCount);
        int newLevel = getLevel(newScavengeCount);
        
        if (newLevel > oldLevel) {
            handleLevelUp(player, oldLevel, newLevel);
            return true;
        }
        
        return false;
    }
    
    /**
     * 处理升级逻辑
     */
    private void handleLevelUp(Player player, int oldLevel, int newLevel) {
        // 发送升级消息
        if (levelConfig.getBoolean("settings.level-up-message", true)) {
            String message = levelConfig.getString("settings.level-up-format", 
                "§6§l恭喜！§e你升级到了 §f{level} §7({name})§e！");
            
            LevelInfo levelInfo = levels.get(newLevel);
            if (levelInfo != null) {
                message = message.replace("{level}", String.valueOf(newLevel))
                               .replace("{name}", levelInfo.getName());
                player.sendMessage(message);
            }
        }
        
        // 给予等级奖励
        if (levelConfig.getBoolean("settings.level-rewards", true)) {
            giveRewards(player, newLevel);
        }
        
        plugin.getLogger().info("玩家 " + player.getName() + " 从等级 " + oldLevel + " 升级到 " + newLevel);
    }
    
    /**
     * 给予等级奖励
     */
    private void giveRewards(Player player, int level) {
        List<String> rewards = levelRewards.get(level);
        if (rewards == null || rewards.isEmpty()) {
            return;
        }
        
        for (String reward : rewards) {
            String command = reward.replace("{player}", player.getName());
            
            try {
                if (command.startsWith("tell ") || command.startsWith("give ")) {
                    // 控制台执行
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
                } else if (command.startsWith("broadcast ")) {
                    // 广播消息
                    String message = command.substring("broadcast ".length());
                    Bukkit.broadcastMessage(message.replace("{player}", player.getName()));
                } else {
                    // 其他命令由控制台执行
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("执行等级奖励命令时出错: " + command + " - " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("给予玩家 " + player.getName() + " 等级 " + level + " 的奖励");
    }
    
    /**
     * 获取等级显示格式
     */
    public String getLevelDisplay(int scavengeCount) {
        int level = getLevel(scavengeCount);
        String name = getLevelName(scavengeCount);
        
        String format = levelConfig.getString("display.gui-format", "§f{level} §7({name})");
        return format.replace("{level}", String.valueOf(level))
                    .replace("{name}", name);
    }
    
    /**
     * 获取等级进度显示
     */
    public String getLevelProgress(int currentScavengeCount) {
        int currentLevel = getLevel(currentScavengeCount);
        LevelInfo nextLevel = getNextLevelInfo(currentScavengeCount);
        
        if (nextLevel == null) {
            // 已达到最高等级
            return levelConfig.getString("display.max-level-format", "§6§l最高等级！");
        }
        
        int remaining = nextLevel.getRequirement() - currentScavengeCount;
        String format = levelConfig.getString("display.progress-format", 
            "§7进度: §f{current}§7/§f{next} §7(还需 §e{remaining}§7 次)");
        
        return format.replace("{current}", String.valueOf(currentScavengeCount))
                    .replace("{next}", String.valueOf(nextLevel.getRequirement()))
                    .replace("{remaining}", String.valueOf(remaining));
    }
    
    /**
     * 获取等级描述
     */
    public String getLevelDescription(int level) {
        LevelInfo levelInfo = levels.get(level);
        if (levelInfo == null || !levelConfig.getBoolean("display.show-description", true)) {
            return "";
        }
        
        String format = levelConfig.getString("display.description-format", "§7{description}");
        return format.replace("{description}", levelInfo.getDescription());
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        levelConfig = YamlConfiguration.loadConfiguration(levelFile);
        loadLevels();
        loadLevelRewards();
        plugin.getLogger().info("等级配置已重新加载");
    }
    
    /**
     * 获取所有等级信息
     */
    public Map<Integer, LevelInfo> getAllLevels() {
        return new HashMap<>(levels);
    }
    
    /**
     * 获取最高等级
     */
    public int getMaxLevel() {
        return levels.keySet().stream().max(Integer::compareTo).orElse(1);
    }
    
    /**
     * 获取配置
     */
    public FileConfiguration getConfig() {
        return levelConfig;
    }
}
