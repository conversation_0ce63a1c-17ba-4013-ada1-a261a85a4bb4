# 搜刮插件任务配置文件
# 根据config.yml中的奖励配置的任务系统
# 任务类型: DAILY(每日), WEEKLY(每周), SPECIAL(特殊)
# 任务目标: SCAVENGE_COUNT(搜刮次数), FIND_RARE_ITEMS(发现稀有物品), COMPLETE_CHESTS(完成搜刮箱), COLLECT_COMMAND_ITEM(收集指令物品)

quests:
  # ==================== 每日任务 ====================
  # 基础搜刮任务
  daily_scavenge_5:
    name: "每日搜刮者"
    description: "完成5次搜刮操作"
    type: "DAILY"
    goal: "SCAVENGE_COUNT"
    target-amount: 5
    rewards:
      - "give {player} diamond 2"
      - "give {player} iron_ingot 5"
      - "give {player} experience_bottle 3"
    console: true
    rare: false

  daily_complete_2_chests:
    name: "每日探索者"
    description: "完成2个搜刮箱的搜刮"
    type: "DAILY"
    goal: "COMPLETE_CHESTS"
    target-amount: 2
    rewards:
      - "give {player} gold_ingot 3"
      - "give {player} bread 5"
      - "give {player} experience_bottle 2"
    console: true
    rare: false


  # ==================== 淬炼石收集任务 ====================
  daily_collect_cuilian_putong:
    name: "普通淬炼石收集者"
    description: "收集3个普通淬炼石"
    type: "DAILY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 3
    target-material: "COAL"
    target-display-name: "&a&l「淬炼石」 - &f&l普通"
    rewards:
      - "give {player} iron_ingot 8"
      - "give {player} bread 5"
      - "give {player} experience_bottle 2"
    console: true
    rare: false

  daily_collect_cuilian_zhongdeng:
    name: "中等淬炼石收集者"
    description: "收集2个中等淬炼石"
    type: "DAILY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 2
    target-material: "COAL"
    target-display-name: "&b&l「淬炼石」 - &a&l中等"
    rewards:
      - "give {player} gold_ingot 4"
      - "give {player} diamond 1"
      - "give {player} experience_bottle 3"
    console: true
    rare: false

  # ==================== 强化宝石收集任务 ====================
  daily_collect_gem_normal:
    name: "粗糙强化素材收集者"
    description: "收集5个粗糙的强化素材"
    type: "DAILY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 5
    target-material: "WOOD"
    target-display-name: "&7&l【粗糙的强化素材】"
    rewards:
      - "give {player} iron_ingot 10"
      - "give {player} bread 8"
      - "give {player} experience_bottle 3"
    console: true
    rare: false

  daily_collect_gem_luck:
    name: "普通强化素材收集者"
    description: "收集3个普通的强化素材"
    type: "DAILY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 3
    target-material: "LOG"
    target-display-name: "&f&l【普通的强化素材】"
    rewards:
      - "give {player} gold_ingot 5"
      - "give {player} diamond 1"
      - "give {player} experience_bottle 4"
    console: true
    rare: false

  daily_collect_gem_safe:
    name: "优秀强化素材收集者"
    description: "收集2个优秀的强化素材"
    type: "DAILY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 2
    target-material: "COAL"
    target-display-name: "&a&l【优秀的强化素材】"
    rewards:
      - "give {player} diamond 2"
      - "give {player} emerald 2"
      - "give {player} experience_bottle 5"
    console: true
    rare: false

  # ==================== 碎片收集任务 ====================
  daily_collect_cuilian_fragments:
    name: "淬炼石碎片收集者"
    description: "收集10个淬炼石碎片"
    type: "DAILY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 10
    target-material: "FLINT"
    target-display-name: "「淬炼石碎片」"
    rewards:
      - "give {player} iron_ingot 8"
      - "give {player} bread 6"
      - "give {player} experience_bottle 2"
    console: true
    rare: false

  daily_collect_gem_fragments:
    name: "强化碎片收集者"
    description: "收集15个强化碎片"
    type: "DAILY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 15
    target-material: "CLAY_BALL"
    target-display-name: "【强化碎片】"
    rewards:
      - "give {player} gold_ingot 6"
      - "give {player} bread 8"
      - "give {player} experience_bottle 3"
    console: true
    rare: false

  # ==================== 每周任务 ====================
  # 基础搜刮任务
  weekly_scavenge_30:
    name: "每周搜刮大师"
    description: "完成30次搜刮操作"
    type: "WEEKLY"
    goal: "SCAVENGE_COUNT"
    target-amount: 30
    rewards:
      - "give {player} diamond 10"
      - "give {player} emerald 5"
      - "give {player} experience_bottle 15"
      - "eco give {player} 500"
    console: true
    rare: true

  weekly_complete_15_chests:
    name: "每周探索大师"
    description: "完成15个搜刮箱的搜刮"
    type: "WEEKLY"
    goal: "COMPLETE_CHESTS"
    target-amount: 15
    rewards:
      - "give {player} diamond 8"
      - "give {player} gold_ingot 20"
      - "give {player} experience_bottle 12"
    console: true
    rare: true


  # ==================== 每周淬炼石收集任务 ====================
  weekly_collect_cuilian_gaodeng:
    name: "高等淬炼石收集者"
    description: "收集3个高等淬炼石"
    type: "WEEKLY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 3
    target-material: "COAL"
    target-display-name: "&5&l「淬炼石」 - &b&l高等"
    rewards:
      - "give {player} diamond 5"
      - "give {player} emerald 3"
      - "give {player} experience_bottle 8"
      - "eco give {player} 400"
    console: true
    rare: true

  weekly_collect_cuilian_wanmei:
    name: "上等淬炼石收集者"
    description: "收集2个上等淬炼石"
    type: "WEEKLY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 2
    target-material: "COAL"
    target-display-name: "&6&l「淬炼石」 - &5&l上等"
    rewards:
      - "give {player} diamond 8"
      - "give {player} emerald 5"
      - "give {player} experience_bottle 12"
      - "eco give {player} 800"
    console: true
    rare: true

  weekly_collect_cuilian_charm:
    name: "淬炼符咒收集者"
    description: "收集1个淬炼符咒"
    type: "WEEKLY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 1
    target-material: "PAPER"
    target-display-name: "&5&l「淬炼符咒」 &b&l- &f&l6"
    rewards:
      - "give {player} diamond 10"
      - "give {player} emerald 8"
      - "give {player} experience_bottle 15"
      - "eco give {player} 1200"
      - "broadcast {player} 收集了珍贵的淬炼符咒！"
    console: true
    rare: true

  # ==================== 每周强化宝石收集任务 ====================
  weekly_collect_gem_vip:
    name: "超级强化素材收集者"
    description: "收集3个超级强化素材"
    type: "WEEKLY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 3
    target-material: "COAL_BLOCK"
    target-display-name: "&b&l【超级强化素材】"
    rewards:
      - "give {player} diamond 8"
      - "give {player} emerald 5"
      - "give {player} experience_bottle 12"
      - "eco give {player} 600"
    console: true
    rare: true

  weekly_collect_gem_direct:
    name: "直升符咒收集者"
    description: "收集1个强化直升符咒"
    type: "WEEKLY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 1
    target-material: "STICK"
    target-display-name: "&e『&6强化直升符咒&e』 &f- &6&l29"
    rewards:
      - "give {player} diamond 12"
      - "give {player} emerald 8"
      - "give {player} experience_bottle 18"
      - "eco give {player} 1500"
      - "broadcast {player} 收集了强大的直升符咒！"
    console: true
    rare: true

  weekly_collect_gem_breakthrough:
    name: "突破石收集者"
    description: "收集1个强化突破石"
    type: "WEEKLY"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 1
    target-material: "EMERALD"
    target-display-name: "&d&l【强化突破石】 &f- &5&l30级"
    rewards:
      - "give {player} diamond 15"
      - "give {player} emerald 10"
      - "give {player} experience_bottle 20"
      - "eco give {player} 2000"
      - "broadcast {player} 收集了珍贵的突破石！"
    console: true
    rare: true

  # ==================== 每周碎片收集任务 ====================
  weekly_collect_advanced_cuilian_fragments:
    name: "高级淬炼石碎片收集者"
    description: "收集20个高级淬炼石碎片（高等+上等+吞噬）"
    type: "WEEKLY"
    goal: "SCAVENGE_COUNT"  # 通过搜刮次数模拟收集高级碎片
    target-amount: 80  # 需要大量搜刮才能获得足够的高级碎片
    rewards:
      - "give {player} diamond 6"
      - "give {player} emerald 4"
      - "give {player} experience_bottle 10"
      - "eco give {player} 500"
    console: true
    rare: true

  weekly_collect_advanced_gem_fragments:
    name: "高级强化碎片收集者"
    description: "收集25个高级强化碎片（超级+符咒+突破）"
    type: "WEEKLY"
    goal: "SCAVENGE_COUNT"  # 通过搜刮次数模拟收集高级碎片
    target-amount: 100  # 需要大量搜刮才能获得足够的高级碎片
    rewards:
      - "give {player} diamond 8"
      - "give {player} emerald 6"
      - "give {player} experience_bottle 12"
      - "eco give {player} 800"
    console: true
    rare: true

  # ==================== 特殊任务 ====================
  # 基础搜刮任务
  special_scavenge_master:
    name: "搜刮宗师"
    description: "完成100次搜刮操作"
    type: "SPECIAL"
    goal: "SCAVENGE_COUNT"
    target-amount: 100
    rewards:
      - "give {player} diamond 64"
      - "give {player} emerald 32"
      - "give {player} gold_ingot 64"
      - "give {player} experience_bottle 50"
      - "eco give {player} 2000"
      - "broadcast {player} 成为了搜刮宗师！"
    console: true
    rare: true

  special_chest_master:
    name: "搜刮箱大师"
    description: "完成50个搜刮箱的搜刮"
    type: "SPECIAL"
    goal: "COMPLETE_CHESTS"
    target-amount: 50
    rewards:
      - "give {player} diamond 48"
      - "give {player} emerald 24"
      - "give {player} iron_ingot 128"
      - "give {player} experience_bottle 40"
      - "eco give {player} 1500"
    console: true
    rare: true

  special_rare_collector:
    name: "稀有物品收藏家"
    description: "发现20个稀有物品"
    type: "SPECIAL"
    goal: "FIND_RARE_ITEMS"
    target-amount: 20
    rewards:
      - "give {player} diamond 32"
      - "give {player} emerald 48"
      - "give {player} experience_bottle 60"
      - "eco give {player} 3000"
      - "broadcast {player} 成为了稀有物品收藏家！"
    console: true
    rare: true

  # ==================== 特殊淬炼石收集任务 ====================
  special_cuilian_master:
    name: "淬炼大师"
    description: "收集所有类型的淬炼石各1个"
    type: "SPECIAL"
    goal: "SCAVENGE_COUNT"  # 通过搜刮次数模拟收集所有淬炼石
    target-amount: 150  # 需要大量搜刮才能获得所有类型
    rewards:
      - "give {player} diamond 30"
      - "give {player} emerald 20"
      - "give {player} experience_bottle 40"
      - "eco give {player} 2500"
      - "broadcast &6{player} &e成为了淬炼大师！掌握了所有淬炼石的奥秘！"
      # 给予所有类型的淬炼石作为奖励
    console: true
    rare: true

  special_cuilian_devour_collector:
    name: "吞噬石收集者"
    description: "收集3个淬炼吞噬石"
    type: "SPECIAL"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 3
    target-material: "COAL"
    target-display-name: "&d&l「淬炼吞噬石」"
    rewards:
      - "give {player} diamond 25"
      - "give {player} emerald 15"
      - "give {player} experience_bottle 30"
      - "eco give {player} 2000"
      - "broadcast &d{player} &e收集了3个危险的淬炼吞噬石！"
    console: true
    rare: true

  special_cuilian_rod_collector:
    name: "直升棒收集者"
    description: "收集2个淬炼直升棒"
    type: "SPECIAL"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 2
    target-material: "STICK"
    target-display-name: "&5&l「淬炼直升棒」 &b&l- &f&l3星"
    rewards:
      - "give {player} diamond 35"
      - "give {player} emerald 25"
      - "give {player} experience_bottle 50"
      - "eco give {player} 3500"
      - "broadcast &5{player} &e收集了2个强大的淬炼直升棒！"
    console: true
    rare: true

  # ==================== 特殊强化宝石收集任务 ====================
  special_gem_master:
    name: "强化大师"
    description: "收集所有类型的强化宝石各1个"
    type: "SPECIAL"
    goal: "SCAVENGE_COUNT"  # 通过搜刮次数模拟收集所有宝石
    target-amount: 200  # 需要大量搜刮才能获得所有类型
    rewards:
      - "give {player} diamond 40"
      - "give {player} emerald 30"
      - "give {player} experience_bottle 60"
      - "eco give {player} 4000"
      - "broadcast &6{player} &e成为了强化大师！掌握了所有强化宝石的奥秘！"
    console: true
    rare: true

  special_gem_rod_collector:
    name: "强化棒收集者"
    description: "收集3个强化棒"
    type: "SPECIAL"
    goal: "COLLECT_COMMAND_ITEM"
    target-amount: 3
    target-material: "BLAZE_ROD"
    target-display-name: "&9&l【强化棒】 &f- &b&l+3级"
    rewards:
      - "give {player} diamond 30"
      - "give {player} emerald 20"
      - "give {player} experience_bottle 40"
      - "eco give {player} 3000"
      - "broadcast &9{player} &e收集了3个强大的强化棒！"
    console: true
    rare: true

  special_ultimate_enhancer:
    name: "终极强化者"
    description: "收集高级强化物品：直升符咒、突破石、强化棒各1个"
    type: "SPECIAL"
    goal: "SCAVENGE_COUNT"  # 通过搜刮次数模拟收集所有高级强化物品
    target-amount: 300  # 需要大量搜刮才能获得所有高级物品
    rewards:
      - "give {player} diamond 60"
      - "give {player} emerald 40"
      - "give {player} experience_bottle 80"
      - "eco give {player} 6000"
      - "broadcast &d&l{player} &e成为了终极强化者！掌握了所有高级强化技术！"
    console: true
    rare: true

  # ==================== 特殊碎片收集任务 ====================
  special_fragment_master:
    name: "碎片大师"
    description: "收集所有类型的碎片各10个"
    type: "SPECIAL"
    goal: "SCAVENGE_COUNT"  # 通过搜刮次数模拟收集所有碎片
    target-amount: 250  # 需要大量搜刮才能获得所有类型碎片
    rewards:
      - "give {player} diamond 35"
      - "give {player} emerald 25"
      - "give {player} experience_bottle 50"
      - "eco give {player} 3000"
      - "broadcast &6{player} &e成为了碎片大师！掌握了所有碎片的奥秘！"
    console: true
    rare: true

  special_fragment_synthesizer:
    name: "碎片合成师"
    description: "通过碎片合成系统制作强化物品"
    type: "SPECIAL"
    goal: "SCAVENGE_COUNT"  # 通过搜刮次数模拟合成活动
    target-amount: 400  # 需要大量搜刮和碎片收集
    rewards:
      - "give {player} diamond 50"
      - "give {player} emerald 35"
      - "give {player} experience_bottle 70"
      - "eco give {player} 4500"
      - "broadcast &b{player} &e成为了碎片合成师！精通碎片合成技术！"
    console: true
    rare: true



# 任务重置时间设置
reset-times:
  daily: "00:00"      # 每日0点重置
  weekly: "MONDAY"    # 每周一重置
  special: 30         # 特殊任务30天重置

# 任务完成音效设置
sounds:
  quest-progress: "EXPERIENCE_ORB_PICKUP"  # 任务进度更新音效
  quest-complete: "LEVEL_UP"               # 任务完成音效
  reward-claim: "ITEM_PICKUP"              # 奖励领取音效
  