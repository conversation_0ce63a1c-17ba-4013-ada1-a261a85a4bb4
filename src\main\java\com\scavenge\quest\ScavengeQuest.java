package com.scavenge.quest;

import java.util.List;
import java.util.UUID;

/**
 * 搜刮任务类
 */
public class ScavengeQuest {

    public enum QuestType {
        DAILY, // 每日任务
        WEEKLY, // 每周任务
        SPECIAL // 特殊任务
    }

    public enum QuestGoal {
        SCAVENGE_COUNT, // 搜刮次数
        FIND_RARE_ITEMS, // 发现稀有物品
        COMPLETE_CHESTS, // 完成搜刮箱
        COLLECT_SPECIFIC, // 收集特定物品
        COLLECT_COMMAND_ITEM // 收集指令给予的物品
    }

    private String id;
    private String name;
    private String description;
    private QuestType type;
    private QuestGoal goal;
    private int targetAmount;
    private List<String> rewards;
    private boolean console;
    private long startTime;
    private long endTime;
    private boolean active;

    // 用于COLLECT_SPECIFIC和COLLECT_COMMAND_ITEM任务
    private String targetMaterial; // 目标物品材质
    private String targetDisplayName; // 目标物品显示名称（可选）

    public ScavengeQuest(String id, String name, String description, QuestType type,
            QuestGoal goal, int targetAmount, List<String> rewards, boolean console) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.goal = goal;
        this.targetAmount = targetAmount;
        this.rewards = rewards;
        this.console = console;
        this.active = true;
        this.targetMaterial = null;
        this.targetDisplayName = null;

        // 时间由QuestManager统一设置，避免冲突
        this.startTime = 0;
        this.endTime = 0;
    }

    // 特定物品任务构造函数
    public ScavengeQuest(String id, String name, String description, QuestType type,
            QuestGoal goal, int targetAmount, List<String> rewards, boolean console,
            String targetMaterial, String targetDisplayName) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.goal = goal;
        this.targetAmount = targetAmount;
        this.rewards = rewards;
        this.console = console;
        this.active = true;
        this.targetMaterial = targetMaterial;
        this.targetDisplayName = targetDisplayName;

        // 时间由QuestManager统一设置，避免冲突
        this.startTime = 0;
        this.endTime = 0;
    }

    /**
     * 检查任务是否过期
     */
    public boolean isExpired() {
        return System.currentTimeMillis() > endTime;
    }

    /**
     * 获取剩余时间（毫秒）
     */
    public long getRemainingTime() {
        return Math.max(0, endTime - System.currentTimeMillis());
    }

    /**
     * 获取剩余时间字符串
     */
    public String getRemainingTimeString() {
        long remaining = getRemainingTime();
        if (remaining <= 0) {
            return "已过期";
        }

        long days = remaining / (24 * 60 * 60 * 1000L);
        long hours = (remaining % (24 * 60 * 60 * 1000L)) / (60 * 60 * 1000L);
        long minutes = (remaining % (60 * 60 * 1000L)) / (60 * 1000L);

        if (days > 0) {
            return days + "天" + hours + "小时";
        } else if (hours > 0) {
            return hours + "小时" + minutes + "分钟";
        } else {
            return minutes + "分钟";
        }
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public QuestType getType() {
        return type;
    }

    public QuestGoal getGoal() {
        return goal;
    }

    public int getTargetAmount() {
        return targetAmount;
    }

    public List<String> getRewards() {
        return rewards;
    }

    public boolean isConsole() {
        return console;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getTargetMaterial() {
        return targetMaterial;
    }

    public String getTargetDisplayName() {
        return targetDisplayName;
    }

    public void setTargetMaterial(String targetMaterial) {
        this.targetMaterial = targetMaterial;
    }

    public void setTargetDisplayName(String targetDisplayName) {
        this.targetDisplayName = targetDisplayName;
    }
}
