package com.scavenge;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 传送垫管理器 - 管理传送垫的位置和目标传送点
 */
public class TeleportPadManager {

    private final ScavengePlugin plugin;
    private final Map<String, TeleportPad> teleportPads;
    private final File dataFile;
    private FileConfiguration dataConfig;
    private Location spawnLocation; // 主城传送点

    public TeleportPadManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.teleportPads = new ConcurrentHashMap<>();
        this.dataFile = new File(plugin.getDataFolder(), "teleport-pads.yml");
        
        loadData();
    }

    /**
     * 加载传送垫数据
     */
    private void loadData() {
        if (!dataFile.exists()) {
            try {
                dataFile.getParentFile().mkdirs();
                dataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().warning("无法创建传送垫数据文件: " + e.getMessage());
                return;
            }
        }

        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
        
        // 加载主城传送点
        if (dataConfig.contains("spawn-location")) {
            try {
                String worldName = dataConfig.getString("spawn-location.world");
                double x = dataConfig.getDouble("spawn-location.x");
                double y = dataConfig.getDouble("spawn-location.y");
                double z = dataConfig.getDouble("spawn-location.z");
                float yaw = (float) dataConfig.getDouble("spawn-location.yaw", 0.0);
                float pitch = (float) dataConfig.getDouble("spawn-location.pitch", 0.0);
                
                if (plugin.getServer().getWorld(worldName) != null) {
                    spawnLocation = new Location(plugin.getServer().getWorld(worldName), x, y, z, yaw, pitch);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("加载主城传送点时出错: " + e.getMessage());
            }
        }

        // 加载传送垫
        if (dataConfig.contains("pads")) {
            for (String locationKey : dataConfig.getConfigurationSection("pads").getKeys(false)) {
                try {
                    String path = "pads." + locationKey;
                    String worldName = dataConfig.getString(path + ".world");
                    int x = dataConfig.getInt(path + ".x");
                    int y = dataConfig.getInt(path + ".y");
                    int z = dataConfig.getInt(path + ".z");
                    
                    if (plugin.getServer().getWorld(worldName) != null) {
                        Location location = new Location(plugin.getServer().getWorld(worldName), x, y, z);
                        TeleportPad pad = new TeleportPad(location);
                        teleportPads.put(locationKey, pad);

                        // 为已存在的传送垫创建粒子效果
                        if (plugin.getParticleManager() != null) {
                            plugin.getParticleManager().createParticleEffect(location);
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("加载传送垫数据时出错: " + locationKey + " - " + e.getMessage());
                }
            }
        }
    }

    /**
     * 保存传送垫数据
     */
    public void saveData() {
        try {
            // 清空现有数据
            dataConfig.set("pads", null);
            
            // 保存主城传送点
            if (spawnLocation != null) {
                dataConfig.set("spawn-location.world", spawnLocation.getWorld().getName());
                dataConfig.set("spawn-location.x", spawnLocation.getX());
                dataConfig.set("spawn-location.y", spawnLocation.getY());
                dataConfig.set("spawn-location.z", spawnLocation.getZ());
                dataConfig.set("spawn-location.yaw", spawnLocation.getYaw());
                dataConfig.set("spawn-location.pitch", spawnLocation.getPitch());
            }

            // 保存所有传送垫
            for (Map.Entry<String, TeleportPad> entry : teleportPads.entrySet()) {
                String locationKey = entry.getKey();
                TeleportPad pad = entry.getValue();
                Location loc = pad.getLocation();
                
                String path = "pads." + locationKey;
                dataConfig.set(path + ".world", loc.getWorld().getName());
                dataConfig.set(path + ".x", loc.getBlockX());
                dataConfig.set(path + ".y", loc.getBlockY());
                dataConfig.set(path + ".z", loc.getBlockZ());
            }

            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().warning("保存传送垫数据时出错: " + e.getMessage());
        }
    }

    /**
     * 创建传送垫
     */
    public boolean createTeleportPad(Location location) {
        if (!plugin.getConfig().getBoolean("teleport-pad.enabled", true)) {
            return false;
        }
        
        String locationKey = getLocationKey(location);
        if (teleportPads.containsKey(locationKey)) {
            return false; // 已存在
        }
        
        // 设置方块材质
        String materialName = plugin.getConfig().getString("teleport-pad.material", "GOLD_PLATE");
        try {
            Material material = Material.valueOf(materialName);
            location.getBlock().setType(material);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的传送垫材质: " + materialName);
            location.getBlock().setType(Material.GOLD_PLATE);
        }
        
        TeleportPad pad = new TeleportPad(location);
        teleportPads.put(locationKey, pad);
        saveData();

        // 创建粒子效果
        if (plugin.getParticleManager() != null) {
            plugin.getParticleManager().createParticleEffect(location);
        }

        return true;
    }

    /**
     * 移除传送垫
     */
    public boolean removeTeleportPad(Location location) {
        String locationKey = getLocationKey(location);
        TeleportPad removed = teleportPads.remove(locationKey);
        
        if (removed != null) {
            // 移除粒子效果
            if (plugin.getParticleManager() != null) {
                plugin.getParticleManager().removeParticleEffect(location);
            }

            // 恢复方块为空气
            location.getBlock().setType(Material.AIR);
            saveData();
            return true;
        }
        
        return false;
    }

    /**
     * 检查位置是否是传送垫
     */
    public boolean isTeleportPad(Location location) {
        String locationKey = getLocationKey(location);
        return teleportPads.containsKey(locationKey);
    }

    /**
     * 获取传送垫
     */
    public TeleportPad getTeleportPad(Location location) {
        String locationKey = getLocationKey(location);
        return teleportPads.get(locationKey);
    }

    /**
     * 设置主城传送点
     */
    public void setSpawnLocation(Location location) {
        this.spawnLocation = location.clone();
        saveData();
    }

    /**
     * 获取主城传送点
     */
    public Location getSpawnLocation() {
        return spawnLocation != null ? spawnLocation.clone() : null;
    }

    /**
     * 生成位置键
     */
    public static String getLocationKey(Location location) {
        return location.getWorld().getName() + ":" +
                location.getBlockX() + ":" +
                location.getBlockY() + ":" +
                location.getBlockZ();
    }

    /**
     * 获取所有传送垫
     */
    public Map<String, TeleportPad> getAllTeleportPads() {
        return teleportPads;
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        saveData();
    }

    /**
     * 传送垫内部类
     */
    public static class TeleportPad {
        private final Location location;

        public TeleportPad(Location location) {
            this.location = location.clone();
        }

        public Location getLocation() {
            return location.clone();
        }

        public String getLocationKey() {
            return TeleportPadManager.getLocationKey(location);
        }
    }
}
