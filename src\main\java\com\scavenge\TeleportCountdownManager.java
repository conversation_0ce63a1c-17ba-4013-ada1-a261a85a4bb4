package com.scavenge;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 传送倒计时管理器 - 管理玩家的传送倒计时
 */
public class TeleportCountdownManager {

    private final ScavengePlugin plugin;
    private final Map<UUID, CountdownTask> activeCountdowns;

    public TeleportCountdownManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.activeCountdowns = new ConcurrentHashMap<>();
    }

    /**
     * 开始传送倒计时
     */
    public boolean startCountdown(Player player, Location padLocation) {
        if (!plugin.getConfig().getBoolean("evacuation-pad.enabled", true)) {
            return false;
        }

        // 检查是否已有倒计时
        if (activeCountdowns.containsKey(player.getUniqueId())) {
            return false;
        }

        // 检查是否有传送目标
        Location spawnLocation = plugin.getTeleportPadManager().getSpawnLocation();
        if (spawnLocation == null) {
            player.sendMessage("§c传送目标未设置！请联系管理员。");
            return false;
        }

        // 创建倒计时任务
        CountdownTask task = new CountdownTask(player, padLocation, spawnLocation);
        activeCountdowns.put(player.getUniqueId(), task);

        // 开始倒计时
        task.start();

        return true;
    }

    /**
     * 取消传送倒计时
     */
    public boolean cancelCountdown(Player player) {
        CountdownTask task = activeCountdowns.remove(player.getUniqueId());
        if (task != null) {
            task.cancel();
            return true;
        }
        return false;
    }

    /**
     * 检查玩家是否在倒计时中
     */
    public boolean hasActiveCountdown(Player player) {
        return activeCountdowns.containsKey(player.getUniqueId());
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        for (CountdownTask task : activeCountdowns.values()) {
            task.cancel();
        }
        activeCountdowns.clear();
    }

    /**
     * 倒计时任务类
     */
    private class CountdownTask {
        private final Player player;
        private final Location padLocation;
        private final Location targetLocation;
        private final int countdownTime;
        private final double cancelDistance;
        private final boolean allowMovement;
        private BukkitTask task;
        private int remainingTime;
        private Location lastPlayerLocation;

        public CountdownTask(Player player, Location padLocation, Location targetLocation) {
            this.player = player;
            this.padLocation = padLocation.clone();
            this.targetLocation = targetLocation.clone();
            this.countdownTime = plugin.getConfig().getInt("evacuation-pad.countdown-time", 5);
            this.cancelDistance = plugin.getConfig().getDouble("evacuation-pad.cancel-distance", 2.0);
            this.allowMovement = plugin.getConfig().getBoolean("evacuation-pad.allow-movement", false);
            this.remainingTime = countdownTime;
            this.lastPlayerLocation = player.getLocation().clone();
        }

        public void start() {
            // 发送开始消息
            if (plugin.getConfig().getBoolean("evacuation-pad.countdown-display.chat.enabled", true)) {
                String startMessage = plugin.getConfig().getString("evacuation-pad.countdown-display.chat.start-message",
                    "&a开始传送倒计时，请保持站立...");
                player.sendMessage(startMessage.replace("&", "§"));
            }

            // 播放开始音效
            playSound("start");

            // 开始倒计时任务
            task = new BukkitRunnable() {
                @Override
                public void run() {
                    if (!player.isOnline()) {
                        cancel();
                        return;
                    }

                    // 检查玩家是否离开传送垫
                    if (player.getLocation().distance(padLocation) > cancelDistance) {
                        cancelWithMessage();
                        return;
                    }

                    // 检查玩家是否移动（如果不允许移动）
                    if (!allowMovement && hasPlayerMoved()) {
                        cancelWithMessage();
                        return;
                    }

                    // 更新显示
                    updateDisplay();

                    // 播放倒计时音效
                    if (remainingTime > 0) {
                        playSound("tick");
                    }

                    // 检查倒计时是否结束
                    if (remainingTime <= 0) {
                        // 执行传送
                        executeTeleport();
                        cancel();
                        return;
                    }

                    remainingTime--;
                    lastPlayerLocation = player.getLocation().clone();
                }
            }.runTaskTimer(plugin, 0L, 20L); // 每秒执行一次
        }

        private boolean hasPlayerMoved() {
            Location current = player.getLocation();
            return current.getBlockX() != lastPlayerLocation.getBlockX() ||
                   current.getBlockY() != lastPlayerLocation.getBlockY() ||
                   current.getBlockZ() != lastPlayerLocation.getBlockZ();
        }

        private void updateDisplay() {
            // 显示标题
            if (plugin.getConfig().getBoolean("evacuation-pad.countdown-display.title.enabled", true)) {
                String titleFormat = plugin.getConfig().getString("evacuation-pad.countdown-display.title.format",
                    "&6&l传送倒计时");
                String title = titleFormat.replace("&", "§");

                String subtitleFormat = plugin.getConfig().getString("evacuation-pad.countdown-display.subtitle.format",
                    "&e{time} 秒后传送到主城");
                String subtitle = subtitleFormat.replace("{time}", String.valueOf(remainingTime)).replace("&", "§");

                // 使用反射发送标题（兼容1.8.8）
                sendTitle(player, title, subtitle);
            }

            // 同时发送聊天消息作为备用
            String chatMessage = "§6传送倒计时: §e" + remainingTime + " 秒";
            player.sendMessage(chatMessage);
        }

        private void cancelWithMessage() {
            // 发送取消消息
            if (plugin.getConfig().getBoolean("evacuation-pad.countdown-display.chat.enabled", true)) {
                String cancelMessage = plugin.getConfig().getString("evacuation-pad.countdown-display.chat.cancel-message",
                    "&c传送已取消！");
                player.sendMessage(cancelMessage.replace("&", "§"));
            }

            // 播放取消音效
            playSound("cancel");

            // 清除标题
            sendTitle(player, "", "");

            cancel();
        }

        private void executeTeleport() {
            // 传送玩家
            player.teleport(targetLocation);

            // 发送成功消息
            if (plugin.getConfig().getBoolean("evacuation-pad.countdown-display.chat.enabled", true)) {
                String successMessage = plugin.getConfig().getString("evacuation-pad.countdown-display.chat.success-message",
                    "&a传送成功！");
                player.sendMessage(successMessage.replace("&", "§"));
            }

            // 播放成功音效
            playSound("success");

            // 清除标题
            sendTitle(player, "", "");
        }

        private void playSound(String soundType) {
            try {
                String soundName = plugin.getConfig().getString("evacuation-pad.sounds." + soundType + ".sound", "");
                if (!soundName.isEmpty()) {
                    Sound sound = Sound.valueOf(soundName);
                    float volume = (float) plugin.getConfig().getDouble("evacuation-pad.sounds." + soundType + ".volume", 1.0);
                    float pitch = (float) plugin.getConfig().getDouble("evacuation-pad.sounds." + soundType + ".pitch", 1.0);
                    player.playSound(player.getLocation(), sound, volume, pitch);
                }
            } catch (Exception e) {
                // 忽略音效错误
            }
        }

        private void sendTitle(Player player, String title, String subtitle) {
            try {
                // 尝试使用简单的方法（如果服务器支持）
                if (hasSimpleTitleMethod()) {
                    player.sendTitle(title, subtitle);
                    return;
                }

                // 1.8.8版本使用反射发送标题
                String version = plugin.getServer().getClass().getPackage().getName().split("\\.")[3];

                Class<?> craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + version + ".entity.CraftPlayer");
                Object craftPlayer = craftPlayerClass.cast(player);
                Object handle = craftPlayerClass.getMethod("getHandle").invoke(craftPlayer);
                Object connection = handle.getClass().getField("playerConnection").get(handle);

                // 创建标题包
                Class<?> packetClass = Class.forName("net.minecraft.server." + version + ".PacketPlayOutTitle");
                Class<?> enumTitleAction = Class.forName("net.minecraft.server." + version + ".PacketPlayOutTitle$EnumTitleAction");
                Class<?> chatComponentText = Class.forName("net.minecraft.server." + version + ".ChatComponentText");

                Object titleAction = enumTitleAction.getField("TITLE").get(null);
                Object subtitleAction = enumTitleAction.getField("SUBTITLE").get(null);

                // 发送标题
                if (!title.isEmpty()) {
                    Object titleComponent = chatComponentText.getConstructor(String.class).newInstance(title);
                    Object titlePacket = packetClass.getConstructor(enumTitleAction, chatComponentText).newInstance(titleAction, titleComponent);
                    connection.getClass().getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet")).invoke(connection, titlePacket);
                }

                // 发送副标题
                if (!subtitle.isEmpty()) {
                    Object subtitleComponent = chatComponentText.getConstructor(String.class).newInstance(subtitle);
                    Object subtitlePacket = packetClass.getConstructor(enumTitleAction, chatComponentText).newInstance(subtitleAction, subtitleComponent);
                    connection.getClass().getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet")).invoke(connection, subtitlePacket);
                }

            } catch (Exception e) {
                // 如果反射失败，发送聊天消息作为替代
                if (!title.isEmpty() || !subtitle.isEmpty()) {
                    String message = title;
                    if (!subtitle.isEmpty()) {
                        message += (message.isEmpty() ? "" : " ") + subtitle;
                    }
                    if (!message.isEmpty()) {
                        player.sendMessage("§6§l[标题] " + message);
                    }
                }
            }
        }

        private boolean hasSimpleTitleMethod() {
            try {
                Player.class.getMethod("sendTitle", String.class, String.class);
                return true;
            } catch (NoSuchMethodException e) {
                return false;
            }
        }

        public void cancel() {
            if (task != null) {
                task.cancel();
            }
            activeCountdowns.remove(player.getUniqueId());
        }
    }
}
