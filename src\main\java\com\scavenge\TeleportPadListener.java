package com.scavenge;

import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

/**
 * 传送垫监听器 - 监听玩家与传送垫的交互
 */
public class TeleportPadListener implements Listener {

    private final ScavengePlugin plugin;

    public TeleportPadListener(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        if (!plugin.getConfig().getBoolean("teleport-pad.enabled", true)) {
            return;
        }

        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();

        // 检查玩家是否移动到了新的方块
        if (from.getBlockX() == to.getBlockX() && 
            from.getBlockY() == to.getBlockY() && 
            from.getBlockZ() == to.getBlockZ()) {
            return;
        }

        // 检查玩家脚下的方块
        Block blockBelow = to.clone().subtract(0, 1, 0).getBlock();
        
        // 检查是否是传送垫
        if (plugin.getTeleportPadManager().isTeleportPad(blockBelow.getLocation())) {
            // 玩家踩到传送垫
            handlePlayerStepOnPad(player, blockBelow.getLocation());
        } else {
            // 玩家离开传送垫，检查是否需要取消倒计时
            if (plugin.getTeleportCountdownManager().hasActiveCountdown(player)) {
                // 检查玩家是否真的离开了传送垫区域
                boolean stillOnPad = false;
                double cancelDistance = plugin.getConfig().getDouble("teleport-pad.cancel-distance", 2.0);
                
                for (TeleportPadManager.TeleportPad pad : plugin.getTeleportPadManager().getAllTeleportPads().values()) {
                    if (player.getLocation().distance(pad.getLocation()) <= cancelDistance) {
                        stillOnPad = true;
                        break;
                    }
                }
                
                if (!stillOnPad) {
                    plugin.getTeleportCountdownManager().cancelCountdown(player);
                }
            }
        }
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // 玩家退出时取消倒计时
        plugin.getTeleportCountdownManager().cancelCountdown(event.getPlayer());
    }

    @EventHandler
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        // 玩家传送时取消倒计时（除非是传送垫传送）
        if (event.getCause() != PlayerTeleportEvent.TeleportCause.PLUGIN) {
            plugin.getTeleportCountdownManager().cancelCountdown(event.getPlayer());
        }
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Block block = event.getBlock();
        Location location = block.getLocation();
        Player player = event.getPlayer();

        // 检查是否是传送垫
        if (plugin.getTeleportPadManager().isTeleportPad(location)) {
            // 只有管理员可以破坏传送垫
            if (!player.hasPermission("scavenge.admin")) {
                event.setCancelled(true);
                player.sendMessage("§c只有管理员可以破坏传送垫！");
                return;
            }

            // 移除传送垫
            plugin.getTeleportPadManager().removeTeleportPad(location);
            player.sendMessage("§a传送垫已移除！");
        }
    }

    /**
     * 处理玩家踩到传送垫
     */
    private void handlePlayerStepOnPad(Player player, Location padLocation) {
        plugin.getLogger().info("玩家 " + player.getName() + " 踩到传送垫: " +
            padLocation.getBlockX() + "," + padLocation.getBlockY() + "," + padLocation.getBlockZ());

        // 检查玩家是否已经在倒计时中
        if (plugin.getTeleportCountdownManager().hasActiveCountdown(player)) {
            plugin.getLogger().info("玩家 " + player.getName() + " 已经在倒计时中，跳过");
            return;
        }

        // 检查权限
        boolean hasUsePermission = player.hasPermission("scavenge.use");
        boolean hasTeleportPermission = player.hasPermission("scavenge.teleport");
        boolean isOp = player.isOp();

        plugin.getLogger().info("玩家 " + player.getName() + " 权限检查: use=" + hasUsePermission +
            ", teleport=" + hasTeleportPermission + ", op=" + isOp);

        if (!hasUsePermission && !hasTeleportPermission && !isOp) {
            player.sendMessage("§c你没有权限使用传送垫！");
            return;
        }

        // 检查世界限制
        if (!plugin.getWorldRestrictionManager().isWorldAllowed(player)) {
            player.sendMessage("§c传送垫在此世界中不可用！");
            plugin.getLogger().info("玩家 " + player.getName() + " 世界限制检查失败");
            return;
        }

        // 检查传送目标
        Location spawnLocation = plugin.getTeleportPadManager().getSpawnLocation();
        if (spawnLocation == null) {
            player.sendMessage("§c传送目标未设置！请联系管理员。");
            plugin.getLogger().warning("传送目标未设置！");
            return;
        }

        plugin.getLogger().info("开始为玩家 " + player.getName() + " 启动传送倒计时");

        // 开始倒计时
        boolean success = plugin.getTeleportCountdownManager().startCountdown(player, padLocation);
        if (!success) {
            player.sendMessage("§c无法开始传送倒计时！");
            plugin.getLogger().warning("为玩家 " + player.getName() + " 启动传送倒计时失败");
        } else {
            plugin.getLogger().info("为玩家 " + player.getName() + " 成功启动传送倒计时");
        }
    }
}
