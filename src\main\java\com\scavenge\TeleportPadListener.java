package com.scavenge;

import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

/**
 * 传送垫监听器 - 监听玩家与传送垫的交互
 */
public class TeleportPadListener implements Listener {

    private final ScavengePlugin plugin;

    public TeleportPadListener(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        if (!plugin.getConfig().getBoolean("evacuation-pad.enabled", true)) {
            plugin.getLogger().info("[调试] 撤离点功能已禁用");
            return;
        }

        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();

        // 检查玩家是否移动到了新的方块
        if (from.getBlockX() == to.getBlockX() &&
            from.getBlockY() == to.getBlockY() &&
            from.getBlockZ() == to.getBlockZ()) {
            return;
        }

        // 检查玩家脚下的方块
        Block blockBelow = to.clone().subtract(0, 1, 0).getBlock();

        plugin.getLogger().info("[调试] 玩家 " + player.getName() + " 移动到: " +
            to.getBlockX() + "," + to.getBlockY() + "," + to.getBlockZ() +
            " 脚下方块: " + blockBelow.getType() + " 位置: " +
            blockBelow.getX() + "," + blockBelow.getY() + "," + blockBelow.getZ());

        // 检查是否是撤离点
        boolean isPad = plugin.getTeleportPadManager().isTeleportPad(blockBelow.getLocation());
        plugin.getLogger().info("[调试] 脚下方块是否为撤离点: " + isPad);

        if (isPad) {
            // 玩家踩到撤离点
            plugin.getLogger().info("[调试] 玩家踩到撤离点，调用处理方法");
            handlePlayerStepOnPad(player, blockBelow.getLocation());
        } else {
            // 玩家离开撤离点，检查是否需要取消倒计时
            if (plugin.getTeleportCountdownManager().hasActiveCountdown(player)) {
                // 检查玩家是否真的离开了撤离点区域
                boolean stillOnPad = false;
                double cancelDistance = plugin.getConfig().getDouble("evacuation-pad.cancel-distance", 2.0);

                for (TeleportPadManager.TeleportPad pad : plugin.getTeleportPadManager().getAllTeleportPads().values()) {
                    if (player.getLocation().distance(pad.getLocation()) <= cancelDistance) {
                        stillOnPad = true;
                        break;
                    }
                }

                if (!stillOnPad) {
                    plugin.getTeleportCountdownManager().cancelCountdown(player);
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("玩家 " + player.getName() + " 离开撤离点区域，取消倒计时");
                    }
                }
            }
        }
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // 玩家退出时取消倒计时
        plugin.getTeleportCountdownManager().cancelCountdown(event.getPlayer());
    }

    @EventHandler
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        // 玩家传送时取消倒计时（除非是传送垫传送）
        if (event.getCause() != PlayerTeleportEvent.TeleportCause.PLUGIN) {
            plugin.getTeleportCountdownManager().cancelCountdown(event.getPlayer());
        }
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Block block = event.getBlock();
        Location location = block.getLocation();
        Player player = event.getPlayer();

        // 检查是否是撤离点
        if (plugin.getTeleportPadManager().isTeleportPad(location)) {
            // 只有管理员可以破坏撤离点
            if (!player.hasPermission("scavenge.admin")) {
                event.setCancelled(true);
                player.sendMessage("§c只有管理员可以破坏撤离点！");
                return;
            }

            // 移除撤离点
            plugin.getTeleportPadManager().removeTeleportPad(location);
            player.sendMessage("§a撤离点已移除！");
        }
    }

    /**
     * 处理玩家踩到撤离点
     */
    private void handlePlayerStepOnPad(Player player, Location padLocation) {
        plugin.getLogger().info("[调试] 开始处理玩家踩踏撤离点: " + player.getName());

        // 检查玩家是否已经在倒计时中
        if (plugin.getTeleportCountdownManager().hasActiveCountdown(player)) {
            plugin.getLogger().info("[调试] 玩家已在倒计时中，跳过");
            return;
        }

        // 检查权限 - 默认允许所有玩家使用撤离点，除非明确禁止
        boolean hasUsePermission = player.hasPermission("scavenge.use");
        boolean hasTeleportPermission = player.hasPermission("scavenge.teleport");
        boolean isOp = player.isOp();
        boolean isDenied = player.hasPermission("scavenge.evacuation.deny");

        plugin.getLogger().info("[调试] 权限检查: use=" + hasUsePermission +
            ", teleport=" + hasTeleportPermission + ", op=" + isOp + ", denied=" + isDenied);

        // 如果明确被拒绝，则不允许使用
        if (isDenied) {
            plugin.getLogger().info("[调试] 玩家被明确拒绝使用撤离点");
            player.sendMessage("§c你没有权限使用撤离点！");
            return;
        }

        plugin.getLogger().info("[调试] 权限检查通过");

        // 检查世界限制
        boolean worldAllowed = plugin.getWorldRestrictionManager().isWorldAllowed(player);
        plugin.getLogger().info("[调试] 世界限制检查: " + worldAllowed);
        if (!worldAllowed) {
            player.sendMessage("§c撤离点在此世界中不可用！");
            return;
        }

        // 检查传送目标
        Location spawnLocation = plugin.getTeleportPadManager().getSpawnLocation();
        plugin.getLogger().info("[调试] 传送目标检查: " + (spawnLocation != null ?
            spawnLocation.getWorld().getName() + " " + spawnLocation.getBlockX() + "," +
            spawnLocation.getBlockY() + "," + spawnLocation.getBlockZ() : "null"));

        if (spawnLocation == null) {
            player.sendMessage("§c传送目标未设置！请联系管理员。");
            return;
        }

        plugin.getLogger().info("[调试] 开始启动倒计时");

        // 开始倒计时
        boolean success = plugin.getTeleportCountdownManager().startCountdown(player, padLocation);
        plugin.getLogger().info("[调试] 倒计时启动结果: " + success);

        if (!success) {
            player.sendMessage("§c无法开始传送倒计时！");
        }
    }
}
