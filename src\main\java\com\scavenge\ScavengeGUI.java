package com.scavenge;

import com.scavenge.quest.ScavengeQuest;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class ScavengeGUI implements Listener {

    private final ScavengePlugin plugin;
    private final Player player;
    private final Inventory inventory;
    private final Set<Integer> unSearchedSlots;
    private final Map<Integer, BukkitTask> searchingTasks;
    private static final Map<UUID, ScavengeGUI> activeGUIs = new ConcurrentHashMap<>();

    public ScavengeGUI(ScavengePlugin plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
        this.unSearchedSlots = new HashSet<>();
        this.searchingTasks = new HashMap<>();

        String title = plugin.getConfig().getString("gui.title", "&6搜刮奖励")
                .replace("&", "§");
        int size = plugin.getConfig().getInt("gui.size", 27);

        this.inventory = Bukkit.createInventory(null, size, title);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        setupGUI();
    }

    private void setupGUI() {
        // 填充背景
        fillBackground();

        // 添加未搜索的物品
        addUnSearchedItems();

        // 添加关闭按钮
        addCloseButton();

        // 开始自动搜索第一个物品
        startNextSearch();
    }

    private void fillBackground() {
        ItemStack glass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 7); // 灰色玻璃板
        ItemMeta meta = glass.getItemMeta();
        meta.setDisplayName("§r");
        glass.setItemMeta(meta);

        // 填充边框
        int size = inventory.getSize();
        for (int i = 0; i < size; i++) {
            if (i < 9 || i >= size - 9 || i % 9 == 0 || i % 9 == 8) {
                inventory.setItem(i, glass);
            }
        }
    }

    // 添加未搜索的物品
    private void addUnSearchedItems() {
        int randomItems = plugin.getConfig().getInt("gui.random-items", 5);
        int[] availableSlots = getAvailableSlots();

        for (int i = 0; i < Math.min(randomItems, availableSlots.length); i++) {
            ItemStack unSearchedItem = plugin.getScavengeManager().createUnSearchedItem();
            inventory.setItem(availableSlots[i], unSearchedItem);
            unSearchedSlots.add(availableSlots[i]);
        }
    }

    // 开始搜索下一个未搜索的物品
    private void startNextSearch() {
        if (unSearchedSlots.isEmpty()) {
            return; // 没有更多未搜索的物品
        }

        // 随机选择一个未搜索的槽位
        Integer[] slots = unSearchedSlots.toArray(new Integer[0]);
        int randomIndex = new Random().nextInt(slots.length);
        int slot = slots[randomIndex];

        // 移除这个槽位从未搜索列表
        unSearchedSlots.remove(slot);

        // 开始搜索动画
        startSearchAnimation(slot);
    }

    // 开始搜索动画
    private void startSearchAnimation(int slot) {
        int duration = plugin.getConfig().getInt("gui.animation.duration", 3);
        int updateInterval = plugin.getConfig().getInt("gui.animation.update-interval", 4);
        int totalUpdates = (duration * 20) / updateInterval; // 总更新次数

        BukkitTask task = new BukkitRunnable() {
            int currentUpdate = 0;

            @Override
            public void run() {
                if (currentUpdate >= totalUpdates) {
                    // 搜索完成，显示奖励
                    completeSearch(slot);
                    this.cancel();
                    return;
                }

                // 更新进度
                int progress = (currentUpdate * 100) / totalUpdates;
                ItemStack searchingItem = plugin.getScavengeManager().createSearchingItem(progress);
                inventory.setItem(slot, searchingItem);

                currentUpdate++;
            }
        }.runTaskTimer(plugin, 0, updateInterval);

        searchingTasks.put(slot, task);
    }

    // 完成搜索，显示奖励
    private void completeSearch(int slot) {
        ScavengeReward reward = plugin.getScavengeManager().selectRandomReward();
        if (reward != null) {
            ItemStack rewardItem = reward.createItemStack();
            if (rewardItem != null && rewardItem.getType() != Material.AIR) {
                inventory.setItem(slot, rewardItem);

                // 记录搜刮统计
                plugin.getLeaderboardManager().recordScavenge(player.getUniqueId(), player.getName());

                // 更新任务进度
                plugin.getQuestManager().updateQuestProgress(player.getUniqueId(),
                        ScavengeQuest.QuestGoal.SCAVENGE_COUNT, 1);

                // 检查是否是稀有物品
                if (reward.isRare()) {
                    plugin.getLeaderboardManager().recordRareItemFound(player.getUniqueId(),
                            player.getName(), rewardItem.getType().name());
                    plugin.getQuestManager().updateQuestProgress(player.getUniqueId(),
                            ScavengeQuest.QuestGoal.FIND_RARE_ITEMS, 1);
                }
            } else {
                // 空奖励，显示空槽位
                inventory.setItem(slot, new ItemStack(Material.AIR));
            }
        }

        // 移除搜索任务
        searchingTasks.remove(slot);

        // 延迟后开始搜索下一个
        int nextSearchDelay = plugin.getConfig().getInt("gui.animation.next-search-delay", 40);
        new BukkitRunnable() {
            @Override
            public void run() {
                startNextSearch();
            }
        }.runTaskLater(plugin, nextSearchDelay);
    }

    private int[] getAvailableSlots() {
        int size = inventory.getSize();
        if (size == 27) {
            return new int[] { 10, 11, 12, 13, 14, 15, 16 };
        } else if (size == 36) {
            return new int[] { 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25 };
        } else if (size == 45) {
            return new int[] { 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34 };
        } else {
            return new int[] { 10, 11, 12, 13, 14, 15, 16 };
        }
    }

    private void addCloseButton() {
        ItemStack closeButton = new ItemStack(Material.BARRIER);
        ItemMeta meta = closeButton.getItemMeta();
        meta.setDisplayName("§c关闭");
        closeButton.setItemMeta(meta);

        inventory.setItem(inventory.getSize() - 5, closeButton);
    }

    public void open() {
        activeGUIs.put(player.getUniqueId(), this);
        player.openInventory(inventory);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player))
            return;

        Player clicker = (Player) event.getWhoClicked();
        if (!clicker.equals(player))
            return;

        if (!event.getInventory().equals(inventory))
            return;

        event.setCancelled(true);

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR)
            return;

        // 检查是否点击了关闭按钮
        if (clickedItem.getType() == Material.BARRIER) {
            player.closeInventory();
            return;
        }

        // 检查是否点击了背景玻璃
        if (clickedItem.getType() == Material.STAINED_GLASS_PANE) {
            return;
        }

        // 检查玩家背包是否有空间
        if (player.getInventory().firstEmpty() == -1) {
            player.sendMessage(plugin.getMessage("inventory-full"));
            return;
        }

        // 给予物品
        ItemStack claimedItem = clickedItem.clone();
        player.getInventory().addItem(claimedItem);
        inventory.setItem(event.getSlot(), new ItemStack(Material.AIR));

        // 更新物品收集任务进度（只有从搜刮箱获得的物品才计入）
        updateItemCollectionProgress(player, claimedItem);

        // 检查是否所有奖励都被拿取
        if (isEmpty()) {
            // 记录搜刮箱完成
            plugin.getLeaderboardManager().recordChestCompleted(player.getUniqueId(), player.getName());
            plugin.getQuestManager().updateQuestProgress(player.getUniqueId(),
                    ScavengeQuest.QuestGoal.COMPLETE_CHESTS, 1);

            player.sendMessage(plugin.getMessage("scavenge-complete"));
            player.closeInventory();
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player))
            return;

        Player closer = (Player) event.getPlayer();
        if (!closer.equals(player))
            return;

        if (!event.getInventory().equals(inventory))
            return;

        // 移除GUI引用
        activeGUIs.remove(player.getUniqueId());

        // 注销事件监听器
        InventoryClickEvent.getHandlerList().unregister(this);
        InventoryCloseEvent.getHandlerList().unregister(this);
    }

    private boolean isEmpty() {
        int[] availableSlots = getAvailableSlots();
        for (int slot : availableSlots) {
            ItemStack item = inventory.getItem(slot);
            if (item != null && item.getType() != Material.AIR) {
                return false;
            }
        }
        return true;
    }

    public static ScavengeGUI getActiveGUI(Player player) {
        return activeGUIs.get(player.getUniqueId());
    }

    /**
     * 更新物品收集任务进度（只有从搜刮箱获得的物品才计入）
     */
    private void updateItemCollectionProgress(Player player, ItemStack item) {
        if (item == null || item.getType() == Material.AIR)
            return;

        String material = item.getType().name();
        String displayName = null;

        // 获取物品显示名称
        ItemMeta meta = item.getItemMeta();
        if (meta != null && meta.hasDisplayName()) {
            displayName = meta.getDisplayName();
        }

        // 更新物品收集任务进度
        plugin.getQuestManager().updateItemCollectionProgress(
                player.getUniqueId(),
                material,
                displayName,
                item.getAmount());
    }
}
