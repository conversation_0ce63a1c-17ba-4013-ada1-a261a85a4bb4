package com.scavenge;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * 碎片工厂类
 * 创建各种淬炼石碎片和宝石碎片
 */
public class FragmentFactory {
    
    /**
     * 创建淬炼石碎片
     */
    public static ItemStack createCuilianFragment(String fragmentType) {
        ItemStack item;
        String displayName;
        List<String> lore = new ArrayList<>();
        
        switch (fragmentType.toLowerCase()) {
            case "putong":
                item = new ItemStack(Material.FLINT);
                displayName = "§7§l「淬炼石碎片」 - §f§l普通";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7普通淬炼石的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整淬炼石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整淬炼石】");
                break;
                
            case "zhongdeng":
                item = new ItemStack(Material.SULPHUR); // 1.8.8中的火药
                displayName = "§8§l「淬炼石碎片」 - §a§l中等";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7中等淬炼石的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整淬炼石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整淬炼石】");
                break;
                
            case "gaodeng":
                item = new ItemStack(Material.REDSTONE);
                displayName = "§c§l「淬炼石碎片」 - §b§l高等";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7高等淬炼石的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整淬炼石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整淬炼石】");
                break;
                
            case "wanmei":
                item = new ItemStack(Material.GLOWSTONE_DUST);
                displayName = "§e§l「淬炼石碎片」 - §5§l上等";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7上等淬炼石的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整淬炼石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整淬炼石】");
                break;
                
            case "huaming":
                item = new ItemStack(Material.NETHER_STALK); // 1.8.8中的地狱疣
                displayName = "§d§l「吞噬石碎片」";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7淬炼吞噬石的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整吞噬石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整吞噬石】");
                break;
                
            default:
                item = new ItemStack(Material.FLINT);
                displayName = "§7§l「淬炼石碎片」";
                lore.add("§7神秘的淬炼石碎片");
                break;
        }
        
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(displayName);
        meta.setLore(lore);
        meta.addEnchant(Enchantment.OXYGEN, 1, true); // 添加发光效果
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 创建宝石碎片
     */
    public static ItemStack createGemFragment(String fragmentType) {
        ItemStack item;
        String displayName;
        List<String> lore = new ArrayList<>();
        
        switch (fragmentType.toLowerCase()) {
            case "normal":
                item = new ItemStack(Material.CLAY_BALL);
                displayName = "§7§l【强化碎片】 - §f§l粗糙";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7粗糙强化素材的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整强化石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整强化石】");
                break;
                
            case "luck":
                item = new ItemStack(Material.SUGAR);
                displayName = "§f§l【强化碎片】 - §a§l普通";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7普通强化素材的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整强化石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整强化石】");
                break;
                
            case "safe":
                item = new ItemStack(Material.QUARTZ_BLOCK); // 1.8.8中使用石英块
                displayName = "§a§l【强化碎片】 - §b§l优秀";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7优秀强化素材的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整强化石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整强化石】");
                break;
                
            case "vip":
                item = new ItemStack(Material.DIAMOND); // 1.8.8中使用钻石
                displayName = "§b§l【强化碎片】 - §6§l超级";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7超级强化素材的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整强化石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整强化石】");
                break;
                
            case "direct":
                item = new ItemStack(Material.EMERALD); // 1.8.8中使用绿宝石
                displayName = "§e§l【符咒碎片】";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7强化直升符咒的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整符咒");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整符咒】");
                break;
                
            case "breakthrough":
                item = new ItemStack(Material.EMERALD_BLOCK); // 1.8.8中使用绿宝石块
                displayName = "§d§l【突破碎片】";
                lore.add("§f==================================");
                lore.add("§e【§c碎片说明§e】:");
                lore.add("§f    - §7强化突破石的碎片");
                lore.add("§f    - §d收集足够碎片可合成完整突破石");
                lore.add("§f==================================");
                lore.add("§7【可通过合成系统制作完整突破石】");
                break;
                
            default:
                item = new ItemStack(Material.CLAY_BALL);
                displayName = "§7§l【强化碎片】";
                lore.add("§7神秘的强化碎片");
                break;
        }
        
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(displayName);
        meta.setLore(lore);
        meta.addEnchant(Enchantment.OXYGEN, 1, true); // 添加发光效果
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 根据类型创建碎片
     */
    public static ItemStack createFragment(String type, String subType) {
        if ("CUILIAN_FRAGMENT".equals(type)) {
            return createCuilianFragment(subType);
        } else if ("GEM_FRAGMENT".equals(type)) {
            return createGemFragment(subType);
        }
        return new ItemStack(Material.FLINT); // 默认返回燧石
    }
    
    /**
     * 检查物品是否是碎片
     */
    public static boolean isFragment(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (!meta.hasDisplayName()) {
            return false;
        }
        
        String displayName = meta.getDisplayName();
        return displayName.contains("碎片") || displayName.contains("【强化碎片】") || 
               displayName.contains("【符咒碎片】") || displayName.contains("【突破碎片】");
    }
}
