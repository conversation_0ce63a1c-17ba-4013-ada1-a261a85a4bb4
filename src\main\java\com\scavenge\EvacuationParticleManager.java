package com.scavenge;

import org.bukkit.Bukkit;
import org.bukkit.Effect;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 撤离区域粒子效果管理器
 */
public class EvacuationParticleManager {
    
    private final ScavengePlugin plugin;
    private final Map<String, BukkitTask> particleTasks;
    
    public EvacuationParticleManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.particleTasks = new ConcurrentHashMap<>();
    }
    
    /**
     * 为撤离区域创建粒子效果
     */
    public void createZoneParticles(EvacuationZoneManager.EvacuationZone zone) {
        if (!plugin.getConfig().getBoolean("evacuation-zone.particles.enabled", true)) {
            return;
        }
        
        // 如果已经有粒子效果，先移除
        removeZoneParticles(zone);
        
        // 获取配置
        String particleType = plugin.getConfig().getString("evacuation-zone.particles.type", "VILLAGER_HAPPY");
        int updateInterval = plugin.getConfig().getInt("evacuation-zone.particles.update-interval", 20);
        double viewDistance = plugin.getConfig().getDouble("evacuation-zone.particles.view-distance", 32.0);
        
        // 创建粒子效果任务
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                // 检查区域是否还存在
                if (!plugin.getEvacuationZoneManager().getAllZones().contains(zone)) {
                    this.cancel();
                    particleTasks.remove(zone.getId());
                    return;
                }
                
                // 检查附近是否有玩家
                boolean hasNearbyPlayer = false;
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getWorld().equals(zone.getWorld()) && 
                        player.getLocation().distance(zone.getCenter()) <= viewDistance) {
                        hasNearbyPlayer = true;
                        break;
                    }
                }
                
                if (!hasNearbyPlayer) {
                    return; // 没有玩家在附近，跳过粒子生成
                }
                
                // 生成边界粒子
                List<Location> borderLocations = zone.getBorderLocations();
                for (Location location : borderLocations) {
                    spawnParticle(location, particleType);
                }
            }
        }.runTaskTimer(plugin, 0L, updateInterval);
        
        particleTasks.put(zone.getId(), task);
        
        plugin.getLogger().info("为撤离区域 '" + zone.getId() + "' 创建粒子效果");
    }
    
    /**
     * 移除撤离区域的粒子效果
     */
    public void removeZoneParticles(EvacuationZoneManager.EvacuationZone zone) {
        BukkitTask task = particleTasks.remove(zone.getId());
        if (task != null) {
            task.cancel();
            plugin.getLogger().info("移除撤离区域 '" + zone.getId() + "' 的粒子效果");
        }
    }
    
    /**
     * 移除所有粒子效果
     */
    public void removeAllParticles() {
        for (BukkitTask task : particleTasks.values()) {
            task.cancel();
        }
        particleTasks.clear();
        plugin.getLogger().info("移除所有撤离区域粒子效果");
    }
    
    /**
     * 重新加载所有区域的粒子效果
     */
    public void reloadAllParticles() {
        // 移除现有粒子效果
        removeAllParticles();
        
        // 为所有区域创建粒子效果
        for (EvacuationZoneManager.EvacuationZone zone : plugin.getEvacuationZoneManager().getAllZones()) {
            createZoneParticles(zone);
        }
    }
    
    /**
     * 生成粒子效果
     */
    private void spawnParticle(Location location, String particleType) {
        try {
            // 1.8.8版本使用Effect枚举
            Effect effect = getEffectFromString(particleType);
            if (effect != null) {
                // 向附近的玩家显示粒子效果
                double viewDistance = plugin.getConfig().getDouble("evacuation-zone.particles.view-distance", 32.0);
                
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getWorld().equals(location.getWorld()) && 
                        player.getLocation().distance(location) <= viewDistance) {
                        player.playEffect(location, effect, null);
                    }
                }
            }
        } catch (Exception e) {
            // 忽略粒子效果错误
        }
    }
    
    /**
     * 将字符串转换为Effect枚举（兼容1.8.8）
     */
    private Effect getEffectFromString(String effectName) {
        try {
            // 尝试直接匹配
            return Effect.valueOf(effectName.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 如果直接匹配失败，尝试一些常见的映射
            switch (effectName.toUpperCase()) {
                case "VILLAGER_HAPPY":
                case "HAPPY_VILLAGER":
                    return Effect.VILLAGER_PLANT_GROW;
                case "PORTAL":
                    return Effect.ENDER_SIGNAL;
                case "ENCHANTMENT_TABLE":
                case "ENCHANT":
                    return Effect.FLYING_GLYPH;
                case "FLAME":
                case "FIRE":
                    return Effect.MOBSPAWNER_FLAMES;
                case "SMOKE":
                    return Effect.SMOKE;
                case "HEART":
                    return Effect.HEART;
                case "MAGIC_CRIT":
                    return Effect.CRIT;
                default:
                    plugin.getLogger().warning("未知的粒子效果类型: " + effectName + "，使用默认效果");
                    return Effect.VILLAGER_PLANT_GROW;
            }
        }
    }
    
    /**
     * 创建选择工具的临时粒子效果
     */
    public void showSelectionParticles(Player player, Location location) {
        if (!plugin.getConfig().getBoolean("evacuation-zone.selection.particles.enabled", true)) {
            return;
        }
        
        String particleType = plugin.getConfig().getString("evacuation-zone.selection.particles.type", "FLAME");
        
        // 在选择点周围显示粒子
        for (int i = 0; i < 8; i++) {
            double angle = (i * Math.PI * 2) / 8;
            double x = location.getX() + Math.cos(angle) * 1.5;
            double z = location.getZ() + Math.sin(angle) * 1.5;
            Location particleLocation = new Location(location.getWorld(), x, location.getY() + 1, z);
            
            spawnParticle(particleLocation, particleType);
        }
        
        // 垂直粒子柱
        for (int y = 0; y < 5; y++) {
            Location particleLocation = location.clone().add(0, y, 0);
            spawnParticle(particleLocation, particleType);
        }
    }
    
    /**
     * 显示区域预览粒子效果
     */
    public void showZonePreview(Player player, Location pos1, Location pos2) {
        if (!plugin.getConfig().getBoolean("evacuation-zone.selection.preview.enabled", true)) {
            return;
        }
        
        if (pos1 == null || pos2 == null || !pos1.getWorld().equals(pos2.getWorld())) {
            return;
        }
        
        // 创建临时区域用于预览
        EvacuationZoneManager.EvacuationZone tempZone = new EvacuationZoneManager.EvacuationZone("preview", pos1, pos2);
        List<Location> borderLocations = tempZone.getBorderLocations();
        
        String particleType = plugin.getConfig().getString("evacuation-zone.selection.preview.type", "ENCHANTMENT_TABLE");
        
        // 显示边界粒子（只显示一部分，避免过多）
        int step = Math.max(1, borderLocations.size() / 50); // 最多显示50个粒子
        for (int i = 0; i < borderLocations.size(); i += step) {
            spawnParticle(borderLocations.get(i), particleType);
        }
    }
}
