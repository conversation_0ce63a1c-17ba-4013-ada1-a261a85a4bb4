package com.scavenge;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * 宝石工厂类
 * 基于宝石系统源代码创建各种强化宝石物品
 */
public class GemStoneFactory {
    
    /**
     * 创建粗糙的强化素材（普通强化石）
     */
    public static ItemStack createNormalGem() {
        ItemStack item = new ItemStack(Material.WOOD);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§7§l【粗糙的强化素材】");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§f -->> §f§l普通几率 §f<<--");
        lore.add("");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f§l - §7放到熔炉里面,烧制武器/装备.");
        lore.add("§f§l - §d(必须是满耐久装备)");
        lore.add("");
        lore.add("§4【强化 +6 装备失败后: 装备自毁】");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 创建普通的强化素材（幸运强化石）
     */
    public static ItemStack createLuckGem() {
        ItemStack item = new ItemStack(Material.LOG);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§f§l【普通的强化素材】");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§f -->> §a§l额外增加 §e§l15% §a§l几率 §f<<--");
        lore.add("");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f§l - §7放到熔炉里面,烧制武器/装备.");
        lore.add("§f§l - §d(必须是满耐久装备)");
        lore.add("");
        lore.add("§4【强化 +6 装备失败后: 装备自毁】");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 创建优秀的强化素材（安全强化石）
     */
    public static ItemStack createSafeGem() {
        ItemStack item = new ItemStack(Material.COAL);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§a§l【优秀的强化素材】");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§f -->> §b§l额外增加 §e§l20% §b§l几率 §f<<--");
        lore.add("");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f§l - §7放到熔炉里面,烧制武器/装备.");
        lore.add("§f§l - §d(必须是满耐久装备)");
        lore.add("");
        lore.add("§e【强化装备失败后,不会自毁】");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 创建超级强化素材（VIP强化石）
     */
    public static ItemStack createVipGem() {
        ItemStack item = new ItemStack(Material.COAL_BLOCK);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§b§l【超级强化素材】");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§f -->> §6§l额外增加 §e§l30% §6§l几率 §f<<--");
        lore.add("");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f§l - §7放到熔炉里面,烧制武器/装备.");
        lore.add("§f§l - §d(必须是满耐久装备)");
        lore.add("");
        lore.add("§e【强化装备失败后,不会自毁】");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 创建管理强化石
     */
    public static ItemStack createAdminGem() {
        ItemStack item = new ItemStack(Material.COAL_BLOCK);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§c§l管理强化石");
        
        List<String> lore = new ArrayList<>();
        lore.add("§a【管理强化石】");
        lore.add("§5(直接强化到最高等级)");
        lore.add("§a【用法】");
        lore.add("§c放到熔炉,燃烧武器/装备");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 创建强化直升符咒
     */
    public static ItemStack createDirectUpgradeCharm(int level, int chance, int requiredLevel) {
        ItemStack item = new ItemStack(Material.STICK);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§e『§6强化直升符咒§e』 §f- §6§l" + level);
        
        List<String> lore = new ArrayList<>();
        lore.add("§7========================");
        lore.add("§f - §e成功几率: §a" + chance + "%");
        lore.add("§7========================");
        lore.add("§f - §e成功后, 直升 §b§l" + level + " §e级");
        lore.add("§7========================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f - §7放到熔炉内,炼制武器,装备");
        lore.add("§f - §d(必须是满耐久装备)");
        lore.add("§7========================");
        
        meta.setLore(lore);
        meta.addEnchant(Enchantment.OXYGEN, 1, true); // 添加发光效果
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 创建强化突破石
     */
    public static ItemStack createBreakthroughStone(int level, int chance) {
        ItemStack item = new ItemStack(Material.EMERALD);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§d§l【强化突破石】 §f- §5§l" + level + "级");
        
        List<String> lore = new ArrayList<>();
        lore.add("§7========================");
        lore.add("§f - §e成功几率: §a" + chance + "%");
        lore.add("§f - §c失败惩罚: §4降级1级");
        lore.add("§7========================");
        lore.add("§f - §e成功后, 突破到 §d§l" + level + " §e级");
        lore.add("§f - §c失败后, 装备等级 §4§l-1");
        lore.add("§7========================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f - §7放到熔炉内,炼制武器,装备");
        lore.add("§f - §d(必须是满耐久装备)");
        lore.add("§7========================");
        
        meta.setLore(lore);
        meta.addEnchant(Enchantment.OXYGEN, 1, true); // 添加发光效果
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 创建强化棒
     */
    public static ItemStack createEnhancementRod(int level, int chance) {
        ItemStack item = new ItemStack(Material.BLAZE_ROD);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§9§l【强化棒】 §f- §b§l+" + level + "级");
        
        List<String> lore = new ArrayList<>();
        lore.add("§7========================");
        lore.add("§f - §e成功几率: §a" + chance + "%");
        lore.add("§f - §c失败惩罚: §7无");
        lore.add("§7========================");
        lore.add("§f - §e成功后, 装备等级 §b§l+" + level);
        lore.add("§f - §a失败后, 无任何惩罚");
        lore.add("§7========================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f - §7放到熔炉内,炼制武器,装备");
        lore.add("§f - §d(必须是满耐久装备)");
        lore.add("§7========================");
        
        meta.setLore(lore);
        meta.addEnchant(Enchantment.OXYGEN, 1, true); // 添加发光效果
        item.setItemMeta(meta);
        
        return item;
    }
    
    /**
     * 根据类型创建宝石
     */
    public static ItemStack createGemStone(String type, int level, int chance) {
        switch (type.toLowerCase()) {
            case "normal":
            case "普通":
                return createNormalGem();
            case "luck":
            case "幸运":
                return createLuckGem();
            case "safe":
            case "安全":
                return createSafeGem();
            case "vip":
            case "超级":
                return createVipGem();
            case "admin":
            case "管理":
                return createAdminGem();
            case "direct":
            case "直升":
                return createDirectUpgradeCharm(level, chance, 0);
            case "breakthrough":
            case "突破":
                return createBreakthroughStone(level, chance);
            case "rod":
            case "强化棒":
                return createEnhancementRod(level, chance);
            default:
                return createNormalGem(); // 默认返回普通强化石
        }
    }
    
    /**
     * 检查物品是否是强化宝石
     */
    public static boolean isGemStone(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (!meta.hasDisplayName()) {
            return false;
        }
        
        String displayName = meta.getDisplayName();
        return displayName.contains("强化素材") || 
               displayName.contains("强化石") || 
               displayName.contains("直升符咒") || 
               displayName.contains("突破石") || 
               displayName.contains("强化棒");
    }
}
