package com.scavenge.utils;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

/**
 * 头颅工具类 - 处理玩家头颅和皮肤
 */
public class SkullUtils {
    
    private static final ConcurrentHashMap<String, String> skinCache = new ConcurrentHashMap<>();
    private static final String MINOTAR_URL = "https://minotar.net/avatar/";
    
    /**
     * 创建玩家头颅物品
     */
    public static ItemStack createPlayerSkull(String playerName) {
        ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3); // 玩家头颅
        SkullMeta meta = (SkullMeta) skull.getItemMeta();
        
        if (meta != null) {
            meta.setOwner(playerName);
            skull.setItemMeta(meta);
        }
        
        return skull;
    }
    
    /**
     * 创建玩家头颅物品（通过UUID）
     */
    public static ItemStack createPlayerSkull(UUID playerId, String playerName) {
        return createPlayerSkull(playerName);
    }
    
    /**
     * 创建带自定义皮肤的头颅
     */
    public static ItemStack createCustomSkull(String playerName, String skinUrl) {
        ItemStack skull = createPlayerSkull(playerName);
        
        // 在1.8.8中，我们主要依赖玩家名称来获取皮肤
        // 自定义皮肤URL功能在更高版本中更容易实现
        
        return skull;
    }
    
    /**
     * 异步获取玩家皮肤URL
     */
    public static CompletableFuture<String> getPlayerSkinUrl(String playerName) {
        return CompletableFuture.supplyAsync(() -> {
            // 检查缓存
            String cachedSkin = skinCache.get(playerName.toLowerCase());
            if (cachedSkin != null) {
                return cachedSkin;
            }
            
            try {
                // 使用Minotar API获取头像
                String skinUrl = MINOTAR_URL + playerName + "/64.png";
                
                // 验证URL是否有效
                if (isValidSkinUrl(skinUrl)) {
                    skinCache.put(playerName.toLowerCase(), skinUrl);
                    return skinUrl;
                }
            } catch (Exception e) {
                // 忽略错误，返回默认
            }
            
            // 返回默认皮肤URL
            String defaultSkin = MINOTAR_URL + "steve/64.png";
            skinCache.put(playerName.toLowerCase(), defaultSkin);
            return defaultSkin;
        });
    }
    
    /**
     * 验证皮肤URL是否有效
     */
    private static boolean isValidSkinUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            return responseCode == 200;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取玩家头像的Minotar URL
     */
    public static String getMinotarUrl(String playerName, int size) {
        return MINOTAR_URL + playerName + "/" + size + ".png";
    }
    
    /**
     * 获取玩家头像的Minotar URL（默认64x64）
     */
    public static String getMinotarUrl(String playerName) {
        return getMinotarUrl(playerName, 64);
    }
    
    /**
     * 清理皮肤缓存
     */
    public static void clearSkinCache() {
        skinCache.clear();
    }
    
    /**
     * 获取缓存大小
     */
    public static int getCacheSize() {
        return skinCache.size();
    }
    
    /**
     * 创建排行榜专用的玩家头颅
     */
    public static ItemStack createLeaderboardSkull(String playerName, int rank) {
        ItemStack skull = createPlayerSkull(playerName);
        SkullMeta meta = (SkullMeta) skull.getItemMeta();
        
        if (meta != null) {
            // 根据排名设置特殊的显示名称
            String rankColor;
            String rankSymbol;
            
            switch (rank) {
                case 1:
                    rankColor = "§6§l";
                    rankSymbol = "👑";
                    break;
                case 2:
                    rankColor = "§7§l";
                    rankSymbol = "🥈";
                    break;
                case 3:
                    rankColor = "§c§l";
                    rankSymbol = "🥉";
                    break;
                default:
                    rankColor = "§f";
                    rankSymbol = "⭐";
                    break;
            }
            
            meta.setDisplayName(rankColor + rankSymbol + " #" + rank + " " + playerName);
            skull.setItemMeta(meta);
        }
        
        return skull;
    }
    
    /**
     * 创建装饰性头颅
     */
    public static ItemStack createDecorativeSkull(String displayName, String ownerName) {
        ItemStack skull = createPlayerSkull(ownerName);
        SkullMeta meta = (SkullMeta) skull.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(displayName);
            skull.setItemMeta(meta);
        }
        
        return skull;
    }
}
