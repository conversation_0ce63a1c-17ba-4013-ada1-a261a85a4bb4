# 搜刮插件配置文件
# Scavenge Plugin Configuration

# 搜刮方块设置
scavenge-block:
  # 搜刮方块的材质类型 (CHEST, ENDER_CHEST, TRAPPED_CHEST)
  material: CHEST
  # 搜刮方块的显示名称
  display-name: "&6&l搜刮箱"
  # 搜刮方块的描述
  lore:
    - "&7右键打开进行搜刮"
    - "&7每次搜刮都有不同的奖励"
    - "&c&l一次性使用"

# 搜刮箱设置
scavenge-chest:
  # 重置时间 (秒)
  reset-time: 300
  # 冷却消息
  cooldown-message: "&c这个搜刮箱还需要等待 {time} 秒才能重新搜刮!"
  # 可用消息
  available-message: "&a搜刮箱已重置，可以重新搜刮了!"

  # 全息图设置
  hologram:
    # 是否启用全息图
    enabled: true
    # 全息图高度偏移 (方块上方多少格)
    height-offset: 1.5
    # 全息图文本格式
    text-format: "&c&l重置倒计时: &f{time}"
    # 更新间隔 (秒)
    update-interval: 1
    # 全息图可见距离 (方块)
    view-distance: 16

# GUI设置
gui:
  # GUI标题
  title: "&6&l搜刮奖励"
  # GUI大小 (9, 18, 27, 36, 45, 54)
  size: 27
  # 默认随机显示的物品数量 (1-27) - 当玩家没有特殊权限时使用
  random-items: 5

  # 权限控制的物品数量设置
  permission-items:
    # VIP权限 - 更多物品
    "scavenge.vip":
      random-items: 8
      display-name: "&6&lVIP搜刮奖励"
    # SVIP权限 - 更多物品
    "scavenge.svip":
      random-items: 12
      display-name: "&e&lSVIP搜刮奖励"
    # MVP权限 - 最多物品
    "scavenge.mvp":
      random-items: 15
      display-name: "&c&lMVP搜刮奖励"
    # 管理员权限 - 满箱物品
    "scavenge.admin":
      random-items: 20
      display-name: "&4&l管理员搜刮奖励"
  # 未搜索物品显示
  unsearched:
    material: STAINED_GLASS_PANE
    data: 7  # 灰色玻璃板
    display-name: "&7未探索"
  # 搜索中物品显示
  searching:
    material: STAINED_GLASS_PANE
    data: 14  # 红色玻璃板
    display-name: "&c&l探索中 {progress}%"
  # 搜索动画设置
  animation:
    # 更新间隔 (tick, 20tick = 1秒) - 3tick确保稳定显示每个数字
    # 总时间 = 100步 × update-interval tick = 100 × 3 = 300tick = 15秒
    update-interval: 3
    # 自动搜索下一个的延迟 (tick)
    next-search-delay: 40
    # 进度条方向设置
    progress-direction:
      # 进度条方向: "countdown" (100%->0%) 或 "countup" (0%->100%)
      mode: "countup"
      # countdown模式: 倒计时，从100%递减到0%，营造紧张感
      # countup模式: 正计时，从0%递增到100%，营造期待感
    # 音效配置
    sounds:
      # 倒计时音效 (每个数字递减时播放) - 与进度条完全同步
      countdown:
        sound: "CLICK"
        volume: 0.3  # 降低音量，因为会频繁播放
        pitch: 1.2   # 稍微提高音调，让声音更清脆
      # 完成音效 (进度条到0%变成物品时播放) - 铁砧放置音效
      complete:
        sound: "ANVIL_LAND"
        volume: 1.0
        pitch: 1.0

# 搜刮奖励物品列表
rewards:
  items:


    # ==================== 淬炼石奖励 ====================
    # 普通淬炼石
    cuilian_stone_putong:
      type: "CUILIAN_STONE"
      stone-type: "putong"
      display-name: "&a&l「淬炼石」 - &f&l普通"
      display-material: COAL
      chance: 8.0  # 8% 几率
      amount: 1-2
      progress-time: 6

    # 中等淬炼石
    cuilian_stone_zhongdeng:
      type: "CUILIAN_STONE"
      stone-type: "zhongdeng"
      display-name: "&b&l「淬炼石」 - &a&l中等"
      display-material: COAL
      chance: 5.0  # 5% 几率
      amount: 1
      progress-time: 8

    # 高等淬炼石
    cuilian_stone_gaodeng:
      type: "CUILIAN_STONE"
      stone-type: "gaodeng"
      display-name: "&5&l「淬炼石」 - &b&l高等"
      display-material: COAL
      chance: 3.0  # 3% 几率
      amount: 1
      progress-time: 10

    # 上等淬炼石
    cuilian_stone_wanmei:
      type: "CUILIAN_STONE"
      stone-type: "wanmei"
      display-name: "&6&l「淬炼石」 - &5&l上等"
      display-material: COAL
      chance: 1.5  # 1.5% 几率
      amount: 1
      progress-time: 12

    # 淬炼吞噬石
    cuilian_stone_huaming:
      type: "CUILIAN_STONE"
      stone-type: "huaming"
      display-name: "&d&l「淬炼吞噬石」"
      display-material: COAL
      chance: 1.0  # 1% 几率
      amount: 1
      progress-time: 15

    # 淬炼符咒
    cuilian_charm:
      type: "CUILIAN_STONE"
      stone-type: "charm"
      level: 6
      display-name: "&5&l「淬炼符咒」 &b&l- &f&l6"
      display-material: PAPER
      chance: 0.8  # 0.8% 几率
      amount: 1
      progress-time: 18

    # 淬炼直升棒
    cuilian_rod:
      type: "CUILIAN_STONE"
      stone-type: "rod"
      level: 3
      display-name: "&5&l「淬炼直升棒」 &b&l- &f&l3星"
      display-material: STICK
      chance: 0.5  # 0.5% 几率
      amount: 1
      progress-time: 20

    # ==================== 强化宝石奖励 ====================
    # 粗糙的强化素材（普通强化石）
    gem_normal:
      type: "GEM_STONE"
      gem-type: "normal"
      display-name: "&7&l【粗糙的强化素材】"
      display-material: WOOD
      chance: 12.0  # 12% 几率
      amount: 1-3
      progress-time: 4

    # 普通的强化素材（幸运强化石）
    gem_luck:
      type: "GEM_STONE"
      gem-type: "luck"
      display-name: "&f&l【普通的强化素材】"
      display-material: LOG
      chance: 8.0  # 8% 几率
      amount: 1-2
      progress-time: 6

    # 优秀的强化素材（安全强化石）
    gem_safe:
      type: "GEM_STONE"
      gem-type: "safe"
      display-name: "&a&l【优秀的强化素材】"
      display-material: COAL
      chance: 5.0  # 5% 几率
      amount: 1
      progress-time: 8

    # 超级强化素材（VIP强化石）
    gem_vip:
      type: "GEM_STONE"
      gem-type: "vip"
      display-name: "&b&l【超级强化素材】"
      display-material: COAL_BLOCK
      chance: 2.0  # 2% 几率
      amount: 1
      progress-time: 12

    # 强化直升符咒
    gem_direct:
      type: "GEM_STONE"
      gem-type: "direct"
      level: 29
      chance-value: 80
      display-name: "&e『&6强化直升符咒&e』 &f- &6&l29"
      display-material: STICK
      chance: 1.0  # 1% 几率
      amount: 1
      progress-time: 15

    # 强化突破石
    gem_breakthrough:
      type: "GEM_STONE"
      gem-type: "breakthrough"
      level: 30
      chance-value: 80
      display-name: "&d&l【强化突破石】 &f- &5&l30级"
      display-material: EMERALD
      chance: 0.8  # 0.8% 几率
      amount: 1
      progress-time: 18

    # 强化棒
    gem_rod:
      type: "GEM_STONE"
      gem-type: "rod"
      level: 3
      chance-value: 70
      display-name: "&9&l【强化棒】 &f- &b&l+3级"
      display-material: BLAZE_ROD
      chance: 0.6  # 0.6% 几率
      amount: 1
      progress-time: 20

    # ==================== 淬炼石碎片奖励 ====================
    # 普通淬炼石碎片
    cuilian_fragment_putong:
      type: "FRAGMENT"
      fragment-type: "CUILIAN_FRAGMENT"
      sub-type: "putong"
      display-name: "&7&l「淬炼石碎片」 - &f&l普通"
      display-material: FLINT
      chance: 25.0  # 25% 几率 - 高概率
      amount: 2-5
      progress-time: 2

    # 中等淬炼石碎片
    cuilian_fragment_zhongdeng:
      type: "FRAGMENT"
      fragment-type: "CUILIAN_FRAGMENT"
      sub-type: "zhongdeng"
      display-name: "&8&l「淬炼石碎片」 - &a&l中等"
      display-material: SULPHUR
      chance: 20.0  # 20% 几率
      amount: 2-4
      progress-time: 3

    # 高等淬炼石碎片
    cuilian_fragment_gaodeng:
      type: "FRAGMENT"
      fragment-type: "CUILIAN_FRAGMENT"
      sub-type: "gaodeng"
      display-name: "&c&l「淬炼石碎片」 - &b&l高等"
      display-material: REDSTONE
      chance: 15.0  # 15% 几率
      amount: 1-3
      progress-time: 4

    # 上等淬炼石碎片
    cuilian_fragment_wanmei:
      type: "FRAGMENT"
      fragment-type: "CUILIAN_FRAGMENT"
      sub-type: "wanmei"
      display-name: "&e&l「淬炼石碎片」 - &5&l上等"
      display-material: GLOWSTONE_DUST
      chance: 8.0  # 8% 几率
      amount: 1-2
      progress-time: 6

    # 吞噬石碎片
    cuilian_fragment_huaming:
      type: "FRAGMENT"
      fragment-type: "CUILIAN_FRAGMENT"
      sub-type: "huaming"
      display-name: "&d&l「吞噬石碎片」"
      display-material: NETHER_STALK
      chance: 5.0  # 5% 几率
      amount: 1-2
      progress-time: 8

    # ==================== 强化宝石碎片奖励 ====================
    # 粗糙强化碎片
    gem_fragment_normal:
      type: "FRAGMENT"
      fragment-type: "GEM_FRAGMENT"
      sub-type: "normal"
      display-name: "&7&l【强化碎片】 - &f&l粗糙"
      display-material: CLAY_BALL
      chance: 30.0  # 30% 几率 - 最高概率
      amount: 3-6
      progress-time: 2

    # 普通强化碎片
    gem_fragment_luck:
      type: "FRAGMENT"
      fragment-type: "GEM_FRAGMENT"
      sub-type: "luck"
      display-name: "&f&l【强化碎片】 - &a&l普通"
      display-material: SUGAR
      chance: 22.0  # 22% 几率
      amount: 2-5
      progress-time: 3

    # 优秀强化碎片
    gem_fragment_safe:
      type: "FRAGMENT"
      fragment-type: "GEM_FRAGMENT"
      sub-type: "safe"
      display-name: "&a&l【强化碎片】 - &b&l优秀"
      display-material: QUARTZ_BLOCK
      chance: 18.0  # 18% 几率
      amount: 2-4
      progress-time: 4

    # 超级强化碎片
    gem_fragment_vip:
      type: "FRAGMENT"
      fragment-type: "GEM_FRAGMENT"
      sub-type: "vip"
      display-name: "&b&l【强化碎片】 - &6&l超级"
      display-material: DIAMOND
      chance: 12.0  # 12% 几率
      amount: 1-3
      progress-time: 5

    # 符咒碎片
    gem_fragment_direct:
      type: "FRAGMENT"
      fragment-type: "GEM_FRAGMENT"
      sub-type: "direct"
      display-name: "&e&l【符咒碎片】"
      display-material: EMERALD
      chance: 6.0  # 6% 几率
      amount: 1-2
      progress-time: 8

    # 突破碎片
    gem_fragment_breakthrough:
      type: "FRAGMENT"
      fragment-type: "GEM_FRAGMENT"
      sub-type: "breakthrough"
      display-name: "&d&l【突破碎片】"
      display-material: EMERALD_BLOCK
      chance: 4.0  # 4% 几率
      amount: 1-2
      progress-time: 10

    # 空奖励 (什么都不给)
    empty:
      material: AIR
      amount: 0
      display-name: "&7空"
      chance: 1.0  # 1% 几率 (降低了空奖励概率)
      progress-time: 8  # 进度条更新间隔(tick) - 很慢

# 世界限制设置
world-restrictions:
  # 是否启用世界限制
  enabled: true
  # 允许使用搜刮箱的世界列表
  allowed-worlds:
    - "world"
    - "world_nether"
    - "world_the_end"
  # 在限制世界中禁止使用的指令列表
  blocked-commands:
    - "spawn"
    - "home"
    - "tp"
    - "teleport"
    - "warp"
    - "back"
    - "tpa"
    - "tpaccept"
    - "tphere"
    - "fly"
    - "gamemode"
    - "gm"

# 消息配置
messages:
  no-permission: "&c你没有权限使用这个命令!"
  player-not-found: "&c找不到玩家: {player}"
  scavenge-given: "&a已给予 {player} 一个搜刮方块!"
  scavenge-received: "&a你收到了一个搜刮方块! 右键放置后可以搜刮!"
  config-reloaded: "&a配置文件已重新加载!"
  block-placed: "&a搜刮箱已放置! 右键点击开始搜刮!"
  scavenge-complete: "&a搜刮完成! 你获得了一些奖励!"
  inventory-full: "&c你的背包已满! 请清理后再试!"
  exploration-started: "&e开始探索新的物品..."
  exploration-complete-found: "&a探索完成！发现了: {item} x{amount}"
  exploration-complete-empty: "&7探索完成！这次没有发现任何物品。"
  all-items-claimed: "&a所有物品都已搜刮完成！等待重置时间后可再次搜刮。"
  force-reset-message: "&c搜刮箱已重置，GUI已关闭。"
  chest-in-use: "&c这个搜刮箱正在被其他玩家使用中!"
  no-admin-permission: "&c只有管理员可以放置或破坏搜刮箱!"
  chest-removed: "&a搜刮箱已成功移除!"
  quest-completed: "&a恭喜! 你完成了任务: {quest}"
  quest-reward-claimed: "&a你已成功领取任务 {quest} 的奖励!"
  world-not-allowed: "&c搜刮箱只能在指定世界中使用!"
  command-blocked: "&c在搜刮世界中禁止使用此指令!"
  chest-over-completed: "&c&l权限不足！&r&e你的权限已降级，此搜刮箱已超出你的权限范围。请等待 &c{time}秒 &e后重置。"

# 调试模式（开启后会输出详细日志）
debug: true

# 撤离区域设置（新系统）
evacuation-zone:
  # 是否启用撤离区域功能
  enabled: true

  # 选择工具设置
  selection-tool:
    # 选择工具材质 (1.8.8兼容)
    material: GOLD_AXE
    # 选择工具显示名称
    display-name: "&6&l撤离区域选择工具"
    # 选择工具描述
    lore:
      - "&7左键点击设置第一个点"
      - "&7右键点击设置第二个点"
      - "&7选择完成后使用指令创建撤离区域"
      - "&e&l管理员专用工具"
    # 选择工具发光效果
    enchanted: true

  # 选择过程设置
  selection:
    # 选择点粒子效果
    particles:
      enabled: true
      type: "FLAME"
    # 区域预览粒子效果
    preview:
      enabled: true
      type: "ENCHANTMENT_TABLE"

  # 倒计时时间 (秒)
  countdown-time: 5

  # 倒计时显示设置
  countdown-display:
    # 标题显示
    title:
      enabled: true
      format: "&6&l撤离倒计时"
    # 副标题显示
    subtitle:
      enabled: true
      format: "&e{time} 秒后传送到安全区域"
    # 聊天消息
    chat:
      enabled: true
      start-message: "&a进入撤离区域，开始传送倒计时..."
      cancel-message: "&c离开撤离区域，传送已取消！"
      success-message: "&a成功撤离到安全区域！"

  # 音效设置
  sounds:
    # 开始倒计时音效
    start:
      sound: "NOTE_PLING"
      volume: 1.0
      pitch: 1.0
    # 倒计时每秒音效
    tick:
      sound: "NOTE_STICKS"
      volume: 0.5
      pitch: 1.5
    # 传送成功音效
    success:
      sound: "ENDERMAN_TELEPORT"
      volume: 1.0
      pitch: 1.0
    # 传送取消音效
    cancel:
      sound: "NOTE_BASS"
      volume: 1.0
      pitch: 0.5

  # 粒子效果设置
  particles:
    # 边界粒子效果（区域边框）
    border:
      # 是否启用边界粒子效果
      enabled: true
      # 粒子类型 (VILLAGER_HAPPY, PORTAL, ENCHANTMENT_TABLE, FLAME, etc.)
      type: "VILLAGER_HAPPY"
      # 粒子更新间隔 (tick, 20tick = 1秒)
      update-interval: 20
      # 粒子可见距离 (方块)
      view-distance: 32

    # 区域内部漩涡粒子效果
    vortex:
      # 是否启用漩涡粒子效果
      enabled: true
      # 粒子类型
      type: "PORTAL"
      # 漩涡中心高度偏移（相对于区域底部）
      height-offset: 2
      # 漩涡半径（方块）
      radius: 3.0
      # 漩涡高度（方块）
      height: 4.0
      # 粒子密度（每圈粒子数量）
      particle-density: 16
      # 漩涡圈数
      spiral-turns: 3
      # 旋转速度（每次更新的角度增量，度）
      rotation-speed: 15
      # 粒子更新间隔 (tick)
      update-interval: 5
      # 粒子可见距离 (方块)
      view-distance: 32



# 任务系统已移至单独的 quests.yml 配置文件
# 请查看 quests.yml 文件来配置任务
