package com.scavenge.leaderboard;

import com.scavenge.ScavengePlugin;
import com.scavenge.level.LevelManager;
import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 排行榜管理器
 */
public class LeaderboardManager {

    public enum LeaderboardType {
        TOTAL_SCAVENGES, // 总搜刮次数
        CHESTS_COMPLETED, // 完成的搜刮箱
        RARE_ITEMS_FOUND, // 发现的稀有物品
        QUESTS_COMPLETED, // 完成的任务
        OVERALL_SCORE // 综合评分
    }

    private final ScavengePlugin plugin;
    private final Map<UUID, PlayerStats> playerStats;
    private File statsFile;
    private FileConfiguration statsConfig;
    private LevelManager levelManager;

    public LeaderboardManager(ScavengePlugin plugin) {
        this.plugin = plugin;
        this.playerStats = new HashMap<>();

        setupStatsFile();
        loadPlayerStats();
        startStatsUpdateTask();
    }

    /**
     * 设置统计数据文件
     */
    private void setupStatsFile() {
        statsFile = new File(plugin.getDataFolder(), "player_stats.yml");
        if (!statsFile.exists()) {
            try {
                statsFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().warning("无法创建统计数据文件: " + e.getMessage());
            }
        }
        statsConfig = YamlConfiguration.loadConfiguration(statsFile);
    }

    /**
     * 加载玩家统计数据
     */
    private void loadPlayerStats() {
        ConfigurationSection playersSection = statsConfig.getConfigurationSection("players");
        if (playersSection == null)
            return;

        for (String playerIdStr : playersSection.getKeys(false)) {
            try {
                UUID playerId = UUID.fromString(playerIdStr);
                ConfigurationSection playerSection = playersSection.getConfigurationSection(playerIdStr);

                String playerName = playerSection.getString("name", "Unknown");
                PlayerStats stats = new PlayerStats(playerId, playerName);

                stats.setTotalScavenges(playerSection.getInt("total-scavenges", 0));
                stats.setChestsCompleted(playerSection.getInt("chests-completed", 0));
                stats.setRareItemsFound(playerSection.getInt("rare-items-found", 0));
                stats.setQuestsCompleted(playerSection.getInt("quests-completed", 0));
                stats.setTotalPlayTime(playerSection.getLong("total-play-time", 0));
                stats.setLastActive(playerSection.getLong("last-active", System.currentTimeMillis()));

                // 加载物品发现统计
                ConfigurationSection itemsSection = playerSection.getConfigurationSection("items-found");
                if (itemsSection != null) {
                    Map<String, Integer> itemsFound = new HashMap<>();
                    for (String itemName : itemsSection.getKeys(false)) {
                        itemsFound.put(itemName, itemsSection.getInt(itemName, 0));
                    }
                    stats.setItemsFound(itemsFound);
                }

                playerStats.put(playerId, stats);
            } catch (Exception e) {
                plugin.getLogger().warning("加载玩家统计数据时出错: " + e.getMessage());
            }
        }

        plugin.getLogger().info("加载了 " + playerStats.size() + " 个玩家的统计数据");
    }

    /**
     * 保存玩家统计数据
     */
    public void savePlayerStats() {
        statsConfig.set("players", null); // 清空现有数据

        for (Map.Entry<UUID, PlayerStats> entry : playerStats.entrySet()) {
            String playerIdStr = entry.getKey().toString();
            PlayerStats stats = entry.getValue();

            String path = "players." + playerIdStr;
            statsConfig.set(path + ".name", stats.getPlayerName());
            statsConfig.set(path + ".total-scavenges", stats.getTotalScavenges());
            statsConfig.set(path + ".chests-completed", stats.getChestsCompleted());
            statsConfig.set(path + ".rare-items-found", stats.getRareItemsFound());
            statsConfig.set(path + ".quests-completed", stats.getQuestsCompleted());
            statsConfig.set(path + ".total-play-time", stats.getTotalPlayTime());
            statsConfig.set(path + ".last-active", stats.getLastActive());

            // 保存物品发现统计
            for (Map.Entry<String, Integer> itemEntry : stats.getItemsFound().entrySet()) {
                statsConfig.set(path + ".items-found." + itemEntry.getKey(), itemEntry.getValue());
            }
        }

        try {
            statsConfig.save(statsFile);
        } catch (IOException e) {
            plugin.getLogger().warning("保存统计数据时出错: " + e.getMessage());
        }
    }

    /**
     * 启动统计数据更新任务
     */
    private void startStatsUpdateTask() {
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            // 更新在线玩家的游戏时间
            updateOnlinePlayersTime();
            // 定期保存数据
            savePlayerStats();
        }, 20L * 60L, 20L * 60L); // 每分钟更新一次
    }

    /**
     * 更新在线玩家的游戏时间
     */
    private void updateOnlinePlayersTime() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            PlayerStats stats = getOrCreatePlayerStats(player.getUniqueId(), player.getName());
            stats.addPlayTime(60 * 1000L); // 增加1分钟游戏时间
        }
    }

    /**
     * 获取或创建玩家统计数据
     */
    public PlayerStats getOrCreatePlayerStats(UUID playerId, String playerName) {
        PlayerStats stats = playerStats.get(playerId);
        if (stats == null) {
            stats = new PlayerStats(playerId, playerName);
            stats.setLevelManager(levelManager); // 设置等级管理器
            playerStats.put(playerId, stats);
        } else {
            // 更新玩家名称（可能已更改）
            stats.setPlayerName(playerName);
            // 确保等级管理器已设置
            if (stats.getLevelManager() == null) {
                stats.setLevelManager(levelManager);
            }
        }
        return stats;
    }

    /**
     * 记录搜刮事件
     */
    public void recordScavenge(UUID playerId, String playerName) {
        plugin.getLogger().info("记录搜刮事件: 玩家=" + playerName + " (" + playerId + ")");
        PlayerStats stats = getOrCreatePlayerStats(playerId, playerName);
        int oldCount = stats.getTotalScavenges();
        stats.addScavenge();
        int newCount = stats.getTotalScavenges();
        plugin.getLogger().info("搜刮次数更新: " + oldCount + " -> " + newCount);

        // 立即保存数据
        savePlayerStats();
    }

    /**
     * 记录搜刮箱完成事件
     */
    public void recordChestCompleted(UUID playerId, String playerName) {
        plugin.getLogger().info("记录搜刮箱完成事件: 玩家=" + playerName + " (" + playerId + ")");
        PlayerStats stats = getOrCreatePlayerStats(playerId, playerName);
        int oldCount = stats.getChestsCompleted();
        stats.addChestCompleted();
        int newCount = stats.getChestsCompleted();
        plugin.getLogger().info("完成箱子数更新: " + oldCount + " -> " + newCount);

        // 立即保存数据
        savePlayerStats();
    }

    /**
     * 记录稀有物品发现事件
     */
    public void recordRareItemFound(UUID playerId, String playerName, String itemName) {
        plugin.getLogger().info("记录稀有物品发现事件: 玩家=" + playerName + ", 物品=" + itemName);
        PlayerStats stats = getOrCreatePlayerStats(playerId, playerName);
        int oldCount = stats.getRareItemsFound();
        stats.addRareItem(itemName);
        int newCount = stats.getRareItemsFound();
        plugin.getLogger().info("稀有物品数更新: " + oldCount + " -> " + newCount);

        // 立即保存数据
        savePlayerStats();
    }

    /**
     * 记录任务完成事件
     */
    public void recordQuestCompleted(UUID playerId, String playerName) {
        plugin.getLogger().info("记录任务完成事件: 玩家=" + playerName + " (" + playerId + ")");
        PlayerStats stats = getOrCreatePlayerStats(playerId, playerName);
        int oldCount = stats.getQuestsCompleted();
        stats.addQuestCompleted();
        int newCount = stats.getQuestsCompleted();
        plugin.getLogger().info("完成任务数更新: " + oldCount + " -> " + newCount);

        // 立即保存数据
        savePlayerStats();
    }

    /**
     * 获取排行榜
     */
    public List<PlayerStats> getLeaderboard(LeaderboardType type, int limit) {
        Comparator<PlayerStats> comparator;

        switch (type) {
            case TOTAL_SCAVENGES:
                comparator = Comparator.comparingInt(PlayerStats::getTotalScavenges).reversed();
                break;
            case CHESTS_COMPLETED:
                comparator = Comparator.comparingInt(PlayerStats::getChestsCompleted).reversed();
                break;
            case RARE_ITEMS_FOUND:
                comparator = Comparator.comparingInt(PlayerStats::getRareItemsFound).reversed();
                break;
            case QUESTS_COMPLETED:
                comparator = Comparator.comparingInt(PlayerStats::getQuestsCompleted).reversed();
                break;
            case OVERALL_SCORE:
                comparator = Comparator.comparingDouble(PlayerStats::calculateScore).reversed();
                break;
            default:
                comparator = Comparator.comparingInt(PlayerStats::getTotalScavenges).reversed();
        }

        return playerStats.values().stream()
                .sorted(comparator)
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * 获取玩家排名
     */
    public int getPlayerRank(UUID playerId, LeaderboardType type) {
        List<PlayerStats> leaderboard = getLeaderboard(type, Integer.MAX_VALUE);
        for (int i = 0; i < leaderboard.size(); i++) {
            if (leaderboard.get(i).getPlayerId().equals(playerId)) {
                return i + 1; // 排名从1开始
            }
        }
        return -1; // 未找到
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        savePlayerStats();
    }

    // Getters
    public Map<UUID, PlayerStats> getPlayerStats() {
        return playerStats;
    }

    public PlayerStats getPlayerStats(UUID playerId) {
        return playerStats.get(playerId);
    }

    /**
     * 设置等级管理器
     */
    public void setLevelManager(LevelManager levelManager) {
        this.levelManager = levelManager;

        // 为所有现有的PlayerStats设置等级管理器
        for (PlayerStats stats : playerStats.values()) {
            stats.setLevelManager(levelManager);
        }
    }

    /**
     * 获取等级管理器
     */
    public LevelManager getLevelManager() {
        return levelManager;
    }
}
