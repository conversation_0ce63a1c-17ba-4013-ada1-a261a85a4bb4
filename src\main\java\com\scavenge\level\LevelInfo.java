package com.scavenge.level;

/**
 * 等级信息类
 */
public class LevelInfo {
    
    private final int level;
    private final String name;
    private final int requirement;
    private final String description;
    
    public LevelInfo(int level, String name, int requirement, String description) {
        this.level = level;
        this.name = name;
        this.requirement = requirement;
        this.description = description;
    }
    
    /**
     * 获取等级数字
     */
    public int getLevel() {
        return level;
    }
    
    /**
     * 获取等级名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取等级要求（搜刮次数）
     */
    public int getRequirement() {
        return requirement;
    }
    
    /**
     * 获取等级描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取完整显示名称
     */
    public String getDisplayName() {
        return level + " (" + name + ")";
    }
    
    @Override
    public String toString() {
        return "LevelInfo{" +
                "level=" + level +
                ", name='" + name + '\'' +
                ", requirement=" + requirement +
                ", description='" + description + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        LevelInfo levelInfo = (LevelInfo) o;
        return level == levelInfo.level;
    }
    
    @Override
    public int hashCode() {
        return level;
    }
}
