package com.scavenge;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 合成统计管理器
 * 跟踪玩家的合成活动和统计数据
 */
public class CraftingStatsManager {
    
    private final JavaPlugin plugin;
    private final File statsFile;
    private FileConfiguration statsConfig;
    
    // 内存中的统计数据缓存
    private final Map<UUID, PlayerCraftingStats> playerStats;
    
    public CraftingStatsManager(JavaPlugin plugin) {
        this.plugin = plugin;
        this.statsFile = new File(plugin.getDataFolder(), "crafting_stats.yml");
        this.playerStats = new HashMap<>();
        
        loadStats();
    }
    
    /**
     * 加载统计数据
     */
    public void loadStats() {
        if (!statsFile.exists()) {
            try {
                statsFile.getParentFile().mkdirs();
                statsFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().warning("无法创建合成统计文件: " + e.getMessage());
                return;
            }
        }
        
        statsConfig = YamlConfiguration.loadConfiguration(statsFile);
        
        // 加载所有玩家的统计数据
        if (statsConfig.contains("players")) {
            for (String uuidStr : statsConfig.getConfigurationSection("players").getKeys(false)) {
                try {
                    UUID uuid = UUID.fromString(uuidStr);
                    PlayerCraftingStats stats = new PlayerCraftingStats();
                    
                    String path = "players." + uuidStr + ".";
                    stats.totalCrafts = statsConfig.getInt(path + "total_crafts", 0);
                    stats.successfulCrafts = statsConfig.getInt(path + "successful_crafts", 0);
                    stats.failedCrafts = statsConfig.getInt(path + "failed_crafts", 0);
                    stats.cuilianCrafts = statsConfig.getInt(path + "cuilian_crafts", 0);
                    stats.gemCrafts = statsConfig.getInt(path + "gem_crafts", 0);
                    stats.specialCrafts = statsConfig.getInt(path + "special_crafts", 0);
                    stats.fragmentsUsed = statsConfig.getInt(path + "fragments_used", 0);
                    stats.lastCraftTime = statsConfig.getLong(path + "last_craft_time", 0);
                    
                    playerStats.put(uuid, stats);
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("无效的UUID格式: " + uuidStr);
                }
            }
        }
        
        plugin.getLogger().info("已加载 " + playerStats.size() + " 个玩家的合成统计数据");
    }
    
    /**
     * 保存统计数据
     */
    public void saveStats() {
        if (statsConfig == null) {
            statsConfig = new YamlConfiguration();
        }
        
        // 清空现有数据
        statsConfig.set("players", null);
        
        // 保存所有玩家的统计数据
        for (Map.Entry<UUID, PlayerCraftingStats> entry : playerStats.entrySet()) {
            String uuidStr = entry.getKey().toString();
            PlayerCraftingStats stats = entry.getValue();
            String path = "players." + uuidStr + ".";
            
            statsConfig.set(path + "total_crafts", stats.totalCrafts);
            statsConfig.set(path + "successful_crafts", stats.successfulCrafts);
            statsConfig.set(path + "failed_crafts", stats.failedCrafts);
            statsConfig.set(path + "cuilian_crafts", stats.cuilianCrafts);
            statsConfig.set(path + "gem_crafts", stats.gemCrafts);
            statsConfig.set(path + "special_crafts", stats.specialCrafts);
            statsConfig.set(path + "fragments_used", stats.fragmentsUsed);
            statsConfig.set(path + "last_craft_time", stats.lastCraftTime);
        }
        
        try {
            statsConfig.save(statsFile);
        } catch (IOException e) {
            plugin.getLogger().warning("无法保存合成统计数据: " + e.getMessage());
        }
    }
    
    /**
     * 记录合成尝试
     */
    public void recordCraftAttempt(Player player, String craftType, boolean success, int fragmentsUsed) {
        UUID uuid = player.getUniqueId();
        PlayerCraftingStats stats = playerStats.computeIfAbsent(uuid, k -> new PlayerCraftingStats());
        
        stats.totalCrafts++;
        stats.lastCraftTime = System.currentTimeMillis();
        stats.fragmentsUsed += fragmentsUsed;
        
        if (success) {
            stats.successfulCrafts++;
        } else {
            stats.failedCrafts++;
        }
        
        // 根据合成类型增加计数
        switch (craftType.toLowerCase()) {
            case "cuilian":
                stats.cuilianCrafts++;
                break;
            case "gem":
                stats.gemCrafts++;
                break;
            case "special":
                stats.specialCrafts++;
                break;
        }
    }
    
    /**
     * 获取玩家统计数据
     */
    public PlayerCraftingStats getPlayerStats(UUID uuid) {
        return playerStats.getOrDefault(uuid, new PlayerCraftingStats());
    }
    
    /**
     * 获取全局统计数据
     */
    public GlobalCraftingStats getGlobalStats() {
        GlobalCraftingStats global = new GlobalCraftingStats();
        
        for (PlayerCraftingStats stats : playerStats.values()) {
            global.totalPlayers++;
            global.totalCrafts += stats.totalCrafts;
            global.successfulCrafts += stats.successfulCrafts;
            global.failedCrafts += stats.failedCrafts;
            global.cuilianCrafts += stats.cuilianCrafts;
            global.gemCrafts += stats.gemCrafts;
            global.specialCrafts += stats.specialCrafts;
            global.fragmentsUsed += stats.fragmentsUsed;
        }
        
        return global;
    }
    
    /**
     * 清理统计数据
     */
    public void cleanup() {
        saveStats();
        playerStats.clear();
    }
    
    /**
     * 玩家合成统计数据类
     */
    public static class PlayerCraftingStats {
        public int totalCrafts = 0;
        public int successfulCrafts = 0;
        public int failedCrafts = 0;
        public int cuilianCrafts = 0;
        public int gemCrafts = 0;
        public int specialCrafts = 0;
        public int fragmentsUsed = 0;
        public long lastCraftTime = 0;
        
        public double getSuccessRate() {
            if (totalCrafts == 0) return 0.0;
            return (double) successfulCrafts / totalCrafts * 100.0;
        }
        
        public double getFailureRate() {
            if (totalCrafts == 0) return 0.0;
            return (double) failedCrafts / totalCrafts * 100.0;
        }
    }
    
    /**
     * 全局合成统计数据类
     */
    public static class GlobalCraftingStats {
        public int totalPlayers = 0;
        public int totalCrafts = 0;
        public int successfulCrafts = 0;
        public int failedCrafts = 0;
        public int cuilianCrafts = 0;
        public int gemCrafts = 0;
        public int specialCrafts = 0;
        public int fragmentsUsed = 0;
        
        public double getSuccessRate() {
            if (totalCrafts == 0) return 0.0;
            return (double) successfulCrafts / totalCrafts * 100.0;
        }
        
        public double getAverageCraftsPerPlayer() {
            if (totalPlayers == 0) return 0.0;
            return (double) totalCrafts / totalPlayers;
        }
    }
}
