package com.scavenge;

import com.scavenge.gui.QuestGUI;
import com.scavenge.gui.LeaderboardGUI;
import com.scavenge.quest.ScavengeQuest;
import com.scavenge.quest.PlayerQuestProgress;
import com.scavenge.leaderboard.PlayerStats;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Map;
import java.util.HashSet;

public class ScavengeCommand implements CommandExecutor {

    private final ScavengePlugin plugin;

    public ScavengeCommand(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    /**
     * 辅助方法：转换颜色代码
     */
    private String colorize(String message) {
        return message.replace("&", "§");
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "give":
                return handleGiveCommand(sender, args);
            case "place":
                return handlePlaceCommand(sender);
            case "remove":
                return handleRemoveCommand(sender);
            case "list":
                return handleListCommand(sender);
            case "reset":
                return handleResetCommand(sender, args);
            case "reload":
                return handleReloadCommand(sender);
            case "quest":
            case "quests":
                return handleQuestCommand(sender);
            case "leaderboard":
            case "lb":
            case "top":
                return handleLeaderboardCommand(sender);
            case "debug":
                return handleDebugCommand(sender, args);
            case "test":
                return handleTestCommand(sender, args);

            case "world":
                return handleWorldCommand(sender, args);
            case "telepad":
            case "pad":
                return handleTelepadCommand(sender, args);
            case "setspawn":
                return handleSetSpawnCommand(sender);
            case "evacuation":
            case "evac":
                return handleEvacuationCommand(sender, args);
            default:
                sendHelp(sender);
                return true;
        }
    }

    private boolean handleGiveCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge give <玩家名> [数量]"));
            return true;
        }

        String playerName = args[1];
        Player target = Bukkit.getPlayer(playerName);

        if (target == null) {
            sender.sendMessage(plugin.getMessage("player-not-found", "player", playerName));
            return true;
        }

        // 解析数量参数
        int amount = 1;
        if (args.length >= 3) {
            try {
                amount = Integer.parseInt(args[2]);
                if (amount <= 0 || amount > 64) {
                    sender.sendMessage(colorize("&c数量必须在1-64之间!"));
                    return true;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage(colorize("&c无效的数量: " + args[2]));
                return true;
            }
        }

        // 检查玩家背包是否有足够空间
        int emptySlots = 0;
        for (ItemStack item : target.getInventory().getContents()) {
            if (item == null) {
                emptySlots++;
            }
        }

        if (emptySlots < amount) {
            sender.sendMessage(colorize("&c玩家背包空间不足! 需要 " + amount + " 个空位，但只有 " + emptySlots + " 个空位。"));
            return true;
        }

        // 给予搜刮方块
        for (int i = 0; i < amount; i++) {
            ItemStack scavengeBlock = plugin.getScavengeManager().createScavengeBlock();
            target.getInventory().addItem(scavengeBlock);
        }

        // 发送消息
        String message = amount == 1 ? plugin.getMessage("scavenge-given", "player", target.getName())
                : colorize("&a已给予 " + target.getName() + " " + amount + " 个搜刮方块!");
        sender.sendMessage(message);

        String receivedMessage = amount == 1 ? plugin.getMessage("scavenge-received")
                : colorize("&a你收到了 " + amount + " 个搜刮方块!");
        target.sendMessage(receivedMessage);

        return true;
    }

    private boolean handlePlaceCommand(CommandSender sender) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;

        // 检查世界限制
        if (!plugin.getWorldRestrictionManager().isWorldAllowed(player)) {
            player.sendMessage(plugin.getMessage("world-not-allowed"));
            return true;
        }

        Location location = player.getTargetBlock(new HashSet<Byte>(), 5).getLocation();

        // 检查是否已经是搜刮箱
        if (plugin.getScavengeChestManager().isScavengeChest(location)) {
            player.sendMessage(colorize("&c这个位置已经是搜刮箱了!"));
            return true;
        }

        // 设置方块为箱子
        String materialName = plugin.getConfig().getString("scavenge-block.material", "CHEST");
        try {
            Material material = Material.valueOf(materialName);
            location.getBlock().setType(material);

            // 创建搜刮箱
            plugin.getScavengeChestManager().getOrCreateChest(location);

            player.sendMessage(colorize("&a搜刮箱已放置在: " +
                    location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ()));
        } catch (IllegalArgumentException e) {
            player.sendMessage(colorize("&c无效的方块材质: " + materialName));
        }

        return true;
    }

    private boolean handleRemoveCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        Location location = player.getTargetBlock(new HashSet<Byte>(), 5).getLocation();

        if (!plugin.getScavengeChestManager().isScavengeChest(location)) {
            player.sendMessage(colorize("&c这个位置不是搜刮箱!"));
            return true;
        }

        // 移除搜刮箱
        plugin.getScavengeChestManager().removeChest(location);
        location.getBlock().setType(Material.AIR);

        player.sendMessage(plugin.getMessage("chest-removed"));
        return true;
    }

    private boolean handleListCommand(CommandSender sender) {
        Map<String, ScavengeChest> chests = plugin.getScavengeChestManager().getAllChests();

        if (chests.isEmpty()) {
            sender.sendMessage(colorize("&e当前没有搜刮箱"));
            return true;
        }

        sender.sendMessage(colorize("&6=== 搜刮箱列表 ==="));
        for (ScavengeChest chest : chests.values()) {
            Location loc = chest.getLocation();
            String status = chest.isCompleted()
                    ? (chest.canReset() ? colorize("&a可重置")
                            : colorize("&c冷却中(" + chest.getRemainingCooldownSeconds() + "秒)"))
                    : colorize("&e进行中");

            sender.sendMessage(colorize("&7" + loc.getWorld().getName() + " " +
                    loc.getBlockX() + "," + loc.getBlockY() + "," + loc.getBlockZ() + " ") + status);
        }

        return true;
    }

    private boolean handleResetCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge reset <all|target>"));
            return true;
        }

        if (args[1].equalsIgnoreCase("all")) {
            // 重置所有搜刮箱
            int count = 0;
            for (ScavengeChest chest : plugin.getScavengeChestManager().getAllChests().values()) {
                chest.reset();
                count++;
            }
            sender.sendMessage(colorize("&a已重置 " + count + " 个搜刮箱!"));
        } else if (args[1].equalsIgnoreCase("target")) {
            if (!(sender instanceof Player)) {
                sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
                return true;
            }

            Player player = (Player) sender;
            Location location = player.getTargetBlock(new HashSet<Byte>(), 5).getLocation();

            ScavengeChest chest = plugin.getScavengeChestManager().getChest(location);
            if (chest == null) {
                player.sendMessage(colorize("&c这个位置不是搜刮箱!"));
                return true;
            }

            chest.reset();
            player.sendMessage(colorize("&a搜刮箱已重置!"));
        }

        return true;
    }

    private boolean handleReloadCommand(CommandSender sender) {
        try {
            plugin.reloadPluginConfig();
            sender.sendMessage(plugin.getMessage("config-reloaded"));
        } catch (Exception e) {
            sender.sendMessage(colorize("&c重新加载配置时出错: " + e.getMessage()));
        }
        return true;
    }

    private boolean handleQuestCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        try {
            QuestGUI questGUI = new QuestGUI(plugin, player);
            questGUI.open();
        } catch (Exception e) {
            player.sendMessage(colorize("&c打开任务界面时出错: " + e.getMessage()));
            e.printStackTrace();
        }
        return true;
    }

    private boolean handleLeaderboardCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        try {
            LeaderboardGUI leaderboardGUI = new LeaderboardGUI(plugin, player);
            leaderboardGUI.open();
        } catch (Exception e) {
            player.sendMessage(colorize("&c打开排行榜界面时出错: " + e.getMessage()));
            e.printStackTrace();
        }
        return true;
    }

    private boolean handleDebugCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge debug <quests|progress|stats>"));
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "quests":
                sender.sendMessage(colorize("&6=== 任务调试信息 ==="));
                for (ScavengeQuest quest : plugin.getQuestManager().getQuests().values()) {
                    sender.sendMessage(colorize("&e任务: " + quest.getName()));
                    sender.sendMessage(colorize("&7- ID: " + quest.getId()));
                    sender.sendMessage(colorize("&7- 类型: " + quest.getType()));
                    sender.sendMessage(colorize("&7- 目标: " + quest.getGoal()));
                    sender.sendMessage(colorize("&7- 活跃: " + quest.isActive()));
                    sender.sendMessage(colorize("&7- 过期: " + quest.isExpired()));
                    sender.sendMessage(colorize("&7- 开始时间: " + quest.getStartTime()));
                    sender.sendMessage(colorize("&7- 结束时间: " + quest.getEndTime()));
                    sender.sendMessage(colorize("&7- 目标数量: " + quest.getTargetAmount()));
                }
                break;

            case "progress":
                if (!(sender instanceof Player)) {
                    sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
                    return true;
                }
                Player player = (Player) sender;
                sender.sendMessage(colorize("&6=== 玩家进度调试信息 ==="));
                Map<String, PlayerQuestProgress> progress = plugin.getQuestManager().getPlayerProgress()
                        .get(player.getUniqueId());
                if (progress != null) {
                    for (Map.Entry<String, PlayerQuestProgress> entry : progress.entrySet()) {
                        sender.sendMessage(colorize("&e任务: " + entry.getKey()));
                        sender.sendMessage(colorize("&7- 进度: " + entry.getValue().getCurrentProgress()));
                        sender.sendMessage(colorize("&7- 完成: " + entry.getValue().isCompleted()));
                        sender.sendMessage(colorize("&7- 已领取: " + entry.getValue().isClaimed()));
                    }
                } else {
                    sender.sendMessage(colorize("&c没有找到玩家进度数据"));
                }
                break;

            case "stats":
                if (!(sender instanceof Player)) {
                    sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
                    return true;
                }
                Player statsPlayer = (Player) sender;
                PlayerStats stats = plugin.getLeaderboardManager().getPlayerStats(statsPlayer.getUniqueId());
                if (stats != null) {
                    sender.sendMessage(colorize("&6=== 玩家统计调试信息 ==="));
                    sender.sendMessage(colorize("&e玩家: " + stats.getPlayerName()));
                    sender.sendMessage(colorize("&7- 总搜刮次数: " + stats.getTotalScavenges()));
                    sender.sendMessage(colorize("&7- 完成箱子: " + stats.getChestsCompleted()));
                    sender.sendMessage(colorize("&7- 稀有物品: " + stats.getRareItemsFound()));
                    sender.sendMessage(colorize("&7- 完成任务: " + stats.getQuestsCompleted()));
                    sender.sendMessage(colorize("&7- 等级: " + stats.getLevel() + " (" + stats.getLevelName() + ")"));
                    sender.sendMessage(colorize("&7- 综合评分: " + String.format("%.1f", stats.calculateScore())));
                } else {
                    sender.sendMessage(colorize("&c没有找到玩家统计数据"));
                }
                break;

            default:
                sender.sendMessage(colorize("&c用法: /scavenge debug <quests|progress|stats>"));
        }
        return true;
    }

    private boolean handleTestCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge test <quest|stats>"));
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "quest":
                plugin.getQuestManager().updateQuestProgress(
                        player.getUniqueId(),
                        ScavengeQuest.QuestGoal.SCAVENGE_COUNT,
                        1);
                player.sendMessage(colorize("&a手动更新了搜刮任务进度!"));
                break;

            case "stats":
                plugin.getLeaderboardManager().recordScavenge(
                        player.getUniqueId(),
                        player.getName());
                player.sendMessage(colorize("&a手动更新了排行榜统计!"));
                break;

            default:
                sender.sendMessage(colorize("&c用法: /scavenge test <quest|stats>"));
        }
        return true;
    }

    private boolean handleWorldCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge world <add|remove|list|enable|disable> [世界名]"));
            return true;
        }

        WorldRestrictionManager worldManager = plugin.getWorldRestrictionManager();

        switch (args[1].toLowerCase()) {
            case "add":
                if (args.length < 3) {
                    sender.sendMessage(colorize("&c用法: /scavenge world add <世界名>"));
                    return true;
                }
                worldManager.addAllowedWorld(args[2]);
                sender.sendMessage(colorize("&a已添加世界 " + args[2] + " 到允许列表!"));
                break;

            case "remove":
                if (args.length < 3) {
                    sender.sendMessage(colorize("&c用法: /scavenge world remove <世界名>"));
                    return true;
                }
                worldManager.removeAllowedWorld(args[2]);
                sender.sendMessage(colorize("&a已从允许列表中移除世界 " + args[2] + "!"));
                break;

            case "list":
                sender.sendMessage(colorize("&6=== 世界限制设置 ==="));
                sender.sendMessage(colorize("&e状态: " + (worldManager.isEnabled() ? "&a启用" : "&c禁用")));
                sender.sendMessage(colorize("&e允许的世界:"));
                for (String world : worldManager.getAllowedWorlds()) {
                    sender.sendMessage(colorize("&7- " + world));
                }
                sender.sendMessage(colorize("&e禁止的指令:"));
                for (String command : worldManager.getBlockedCommands()) {
                    sender.sendMessage(colorize("&7- /" + command));
                }
                break;

            case "enable":
                worldManager.setEnabled(true);
                sender.sendMessage(colorize("&a已启用世界限制功能!"));
                break;

            case "disable":
                worldManager.setEnabled(false);
                sender.sendMessage(colorize("&c已禁用世界限制功能!"));
                break;

            default:
                sender.sendMessage(colorize("&c用法: /scavenge world <add|remove|list|enable|disable> [世界名]"));
        }

        return true;
    }

    private boolean handleTelepadCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge telepad <place|remove|list>"));
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "place":
                return handleTelepadPlaceCommand(sender);
            case "remove":
                return handleTelepadRemoveCommand(sender);
            case "list":
                return handleTelepadListCommand(sender);
            default:
                sender.sendMessage(colorize("&c用法: /scavenge telepad <place|remove|list>"));
        }

        return true;
    }

    private boolean handleTelepadPlaceCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        Location location = player.getTargetBlock(new HashSet<Byte>(), 5).getLocation();

        // 检查是否已经是传送垫
        if (plugin.getTeleportPadManager().isTeleportPad(location)) {
            player.sendMessage(colorize("&c这个位置已经是传送垫了!"));
            return true;
        }

        // 创建传送垫
        boolean success = plugin.getTeleportPadManager().createTeleportPad(location);
        if (success) {
            player.sendMessage(colorize("&a传送垫已放置在: " +
                    location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ()));
        } else {
            player.sendMessage(colorize("&c创建传送垫失败!"));
        }

        return true;
    }

    private boolean handleTelepadRemoveCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        Location location = player.getTargetBlock(new HashSet<Byte>(), 5).getLocation();

        if (!plugin.getTeleportPadManager().isTeleportPad(location)) {
            player.sendMessage(colorize("&c这个位置不是传送垫!"));
            return true;
        }

        // 移除传送垫
        boolean success = plugin.getTeleportPadManager().removeTeleportPad(location);
        if (success) {
            player.sendMessage(colorize("&a传送垫已移除!"));
        } else {
            player.sendMessage(colorize("&c移除传送垫失败!"));
        }

        return true;
    }

    private boolean handleTelepadListCommand(CommandSender sender) {
        Map<String, TeleportPadManager.TeleportPad> pads = plugin.getTeleportPadManager().getAllTeleportPads();

        if (pads.isEmpty()) {
            sender.sendMessage(colorize("&e当前没有传送垫"));
            return true;
        }

        sender.sendMessage(colorize("&6=== 传送垫列表 ==="));
        for (TeleportPadManager.TeleportPad pad : pads.values()) {
            Location loc = pad.getLocation();
            sender.sendMessage(colorize("&7" + loc.getWorld().getName() + " " +
                    loc.getBlockX() + "," + loc.getBlockY() + "," + loc.getBlockZ()));
        }

        // 显示传送目标
        Location spawnLocation = plugin.getTeleportPadManager().getSpawnLocation();
        if (spawnLocation != null) {
            sender.sendMessage(colorize("&e传送目标: &7" + spawnLocation.getWorld().getName() + " " +
                    spawnLocation.getBlockX() + "," + spawnLocation.getBlockY() + "," + spawnLocation.getBlockZ()));
        } else {
            sender.sendMessage(colorize("&c传送目标未设置! 使用 /scavenge setspawn 设置"));
        }

        return true;
    }

    private boolean handleSetSpawnCommand(CommandSender sender) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        Location location = player.getLocation();

        // 设置传送目标
        plugin.getTeleportPadManager().setSpawnLocation(location);
        player.sendMessage(colorize("&a传送目标已设置为当前位置: " +
                location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ()));

        return true;
    }

    private boolean handleEvacuationCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge evacuation <give> [玩家名] [数量]"));
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "give":
                return handleEvacuationGiveCommand(sender, args);
            default:
                sender.sendMessage(colorize("&c用法: /scavenge evacuation <give> [玩家名] [数量]"));
        }

        return true;
    }

    private boolean handleEvacuationGiveCommand(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(colorize("&c用法: /scavenge evacuation give <玩家名> [数量]"));
            return true;
        }

        String playerName = args[2];
        Player target = Bukkit.getPlayer(playerName);

        if (target == null) {
            sender.sendMessage(plugin.getMessage("player-not-found", "player", playerName));
            return true;
        }

        // 解析数量参数
        int amount = 1;
        if (args.length >= 4) {
            try {
                amount = Integer.parseInt(args[3]);
                if (amount <= 0 || amount > 64) {
                    sender.sendMessage(colorize("&c数量必须在1-64之间!"));
                    return true;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage(colorize("&c无效的数量: " + args[3]));
                return true;
            }
        }

        // 检查玩家背包是否有足够空间
        int emptySlots = 0;
        for (ItemStack item : target.getInventory().getContents()) {
            if (item == null) {
                emptySlots++;
            }
        }

        if (emptySlots < amount) {
            sender.sendMessage(colorize("&c玩家背包空间不足! 需要 " + amount + " 个空位，但只有 " + emptySlots + " 个空位。"));
            return true;
        }

        // 给予撤离点物品
        for (int i = 0; i < amount; i++) {
            ItemStack evacuationItem = plugin.getEvacuationManager().createEvacuationItem();
            target.getInventory().addItem(evacuationItem);
        }

        // 发送消息
        String message = amount == 1 ? colorize("&a已给予 " + target.getName() + " 一个撤离点物品!")
                : colorize("&a已给予 " + target.getName() + " " + amount + " 个撤离点物品!");
        sender.sendMessage(message);

        String receivedMessage = amount == 1 ? colorize("&a你收到了一个撤离点物品! 右键放置创建撤离点!")
                : colorize("&a你收到了 " + amount + " 个撤离点物品! 右键放置创建撤离点!");
        target.sendMessage(receivedMessage);

        return true;
    }

    private void sendHelp(CommandSender sender) {
        sender.sendMessage(colorize("&6=== 搜刮插件帮助 ==="));
        sender.sendMessage(colorize("&e/scavenge give <玩家名> [数量] &7- 给予玩家搜刮方块"));
        sender.sendMessage(colorize("&e/scavenge place &7- 在目标位置放置搜刮箱"));
        sender.sendMessage(colorize("&e/scavenge remove &7- 移除目标位置的搜刮箱"));
        sender.sendMessage(colorize("&e/scavenge list &7- 列出所有搜刮箱"));
        sender.sendMessage(colorize("&e/scavenge reset <all|target> &7- 重置搜刮箱"));
        sender.sendMessage(colorize("&e/scavenge quest &7- 打开任务界面"));
        sender.sendMessage(colorize("&e/scavenge leaderboard &7- 打开排行榜"));
        sender.sendMessage(colorize("&e/scavenge world <add|remove|list|enable|disable> &7- 管理世界限制"));
        sender.sendMessage(colorize("&e/scavenge telepad <place|remove|list> &7- 管理传送垫"));
        sender.sendMessage(colorize("&e/scavenge setspawn &7- 设置传送目标位置"));
        sender.sendMessage(colorize("&e/scavenge evacuation give <玩家名> [数量] &7- 给予撤离点物品"));
        sender.sendMessage(colorize("&e/scavenge reload &7- 重新加载配置文件"));
    }
}
