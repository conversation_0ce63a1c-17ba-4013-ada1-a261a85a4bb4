package com.scavenge;

import com.scavenge.gui.QuestGUI;
import com.scavenge.gui.LeaderboardGUI;
import com.scavenge.quest.ScavengeQuest;
import com.scavenge.quest.PlayerQuestProgress;
import com.scavenge.leaderboard.PlayerStats;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Map;
import java.util.HashSet;

public class ScavengeCommand implements CommandExecutor {

    private final ScavengePlugin plugin;

    public ScavengeCommand(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    /**
     * 辅助方法：转换颜色代码
     */
    private String colorize(String message) {
        return message.replace("&", "§");
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "give":
                return handleGiveCommand(sender, args);
            case "zone":
                return handleZoneCommand(sender, args);
            case "place":
                return handlePlaceCommand(sender);
            case "remove":
                return handleRemoveCommand(sender);
            case "list":
                return handleListCommand(sender);
            case "reset":
                return handleResetCommand(sender, args);
            case "reload":
                return handleReloadCommand(sender);
            case "quest":
            case "quests":
                return handleQuestCommand(sender);
            case "leaderboard":
            case "lb":
            case "top":
                return handleLeaderboardCommand(sender);
            case "debug":
                return handleDebugCommand(sender, args);
            case "test":
                return handleTestCommand(sender, args);

            case "world":
                return handleWorldCommand(sender, args);
            case "setspawn":
                return handleSetSpawnCommand(sender);
            case "evacuation":
            case "evac":
                // 重定向到新的区域系统
                return handleZoneCommand(sender, args);
            case "crafting":
            case "craft":
                return handleCraftingCommand(sender, args);
            default:
                sendHelp(sender);
                return true;
        }
    }

    private boolean handleGiveCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge give <玩家名> [数量]"));
            return true;
        }

        String playerName = args[1];
        Player target = Bukkit.getPlayer(playerName);

        if (target == null) {
            sender.sendMessage(plugin.getMessage("player-not-found", "player", playerName));
            return true;
        }

        // 解析数量参数
        int amount = 1;
        if (args.length >= 3) {
            try {
                amount = Integer.parseInt(args[2]);
                if (amount <= 0 || amount > 64) {
                    sender.sendMessage(colorize("&c数量必须在1-64之间!"));
                    return true;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage(colorize("&c无效的数量: " + args[2]));
                return true;
            }
        }

        // 检查玩家背包是否有足够空间
        int emptySlots = 0;
        for (ItemStack item : target.getInventory().getContents()) {
            if (item == null) {
                emptySlots++;
            }
        }

        if (emptySlots < amount) {
            sender.sendMessage(colorize("&c玩家背包空间不足! 需要 " + amount + " 个空位，但只有 " + emptySlots + " 个空位。"));
            return true;
        }

        // 给予搜刮方块
        for (int i = 0; i < amount; i++) {
            ItemStack scavengeBlock = plugin.getScavengeManager().createScavengeBlock();
            target.getInventory().addItem(scavengeBlock);
        }

        // 发送消息
        String message = amount == 1 ? plugin.getMessage("scavenge-given", "player", target.getName())
                : colorize("&a已给予 " + target.getName() + " " + amount + " 个搜刮方块!");
        sender.sendMessage(message);

        String receivedMessage = amount == 1 ? plugin.getMessage("scavenge-received")
                : colorize("&a你收到了 " + amount + " 个搜刮方块!");
        target.sendMessage(receivedMessage);

        return true;
    }

    private boolean handlePlaceCommand(CommandSender sender) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;

        // 检查世界限制
        if (!plugin.getWorldRestrictionManager().isWorldAllowed(player)) {
            player.sendMessage(plugin.getMessage("world-not-allowed"));
            return true;
        }

        Location location = player.getTargetBlock(new HashSet<Byte>(), 5).getLocation();

        // 检查是否已经是搜刮箱
        if (plugin.getScavengeChestManager().isScavengeChest(location)) {
            player.sendMessage(colorize("&c这个位置已经是搜刮箱了!"));
            return true;
        }

        // 设置方块为箱子
        String materialName = plugin.getConfig().getString("scavenge-block.material", "CHEST");
        try {
            Material material = Material.valueOf(materialName);
            location.getBlock().setType(material);

            // 创建搜刮箱
            plugin.getScavengeChestManager().getOrCreateChest(location);

            player.sendMessage(colorize("&a搜刮箱已放置在: " +
                    location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ()));
        } catch (IllegalArgumentException e) {
            player.sendMessage(colorize("&c无效的方块材质: " + materialName));
        }

        return true;
    }

    private boolean handleRemoveCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        Location location = player.getTargetBlock(new HashSet<Byte>(), 5).getLocation();

        if (!plugin.getScavengeChestManager().isScavengeChest(location)) {
            player.sendMessage(colorize("&c这个位置不是搜刮箱!"));
            return true;
        }

        // 移除搜刮箱
        plugin.getScavengeChestManager().removeChest(location);
        location.getBlock().setType(Material.AIR);

        player.sendMessage(plugin.getMessage("chest-removed"));
        return true;
    }

    private boolean handleListCommand(CommandSender sender) {
        Map<String, ScavengeChest> chests = plugin.getScavengeChestManager().getAllChests();

        if (chests.isEmpty()) {
            sender.sendMessage(colorize("&e当前没有搜刮箱"));
            return true;
        }

        sender.sendMessage(colorize("&6=== 搜刮箱列表 ==="));
        for (ScavengeChest chest : chests.values()) {
            Location loc = chest.getLocation();
            String status = chest.isCompleted()
                    ? (chest.canReset() ? colorize("&a可重置")
                            : colorize("&c冷却中(" + chest.getRemainingCooldownSeconds() + "秒)"))
                    : colorize("&e进行中");

            sender.sendMessage(colorize("&7" + loc.getWorld().getName() + " " +
                    loc.getBlockX() + "," + loc.getBlockY() + "," + loc.getBlockZ() + " ") + status);
        }

        return true;
    }

    private boolean handleResetCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge reset <all|target>"));
            return true;
        }

        if (args[1].equalsIgnoreCase("all")) {
            // 重置所有搜刮箱
            int count = 0;
            for (ScavengeChest chest : plugin.getScavengeChestManager().getAllChests().values()) {
                chest.reset();
                count++;
            }
            sender.sendMessage(colorize("&a已重置 " + count + " 个搜刮箱!"));
        } else if (args[1].equalsIgnoreCase("target")) {
            if (!(sender instanceof Player)) {
                sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
                return true;
            }

            Player player = (Player) sender;
            Location location = player.getTargetBlock(new HashSet<Byte>(), 5).getLocation();

            ScavengeChest chest = plugin.getScavengeChestManager().getChest(location);
            if (chest == null) {
                player.sendMessage(colorize("&c这个位置不是搜刮箱!"));
                return true;
            }

            chest.reset();
            player.sendMessage(colorize("&a搜刮箱已重置!"));
        }

        return true;
    }

    private boolean handleReloadCommand(CommandSender sender) {
        try {
            plugin.reloadPluginConfig();
            sender.sendMessage(plugin.getMessage("config-reloaded"));
        } catch (Exception e) {
            sender.sendMessage(colorize("&c重新加载配置时出错: " + e.getMessage()));
        }
        return true;
    }

    private boolean handleQuestCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        try {
            QuestGUI questGUI = new QuestGUI(plugin, player);
            questGUI.open();
        } catch (Exception e) {
            player.sendMessage(colorize("&c打开任务界面时出错: " + e.getMessage()));
            e.printStackTrace();
        }
        return true;
    }

    private boolean handleLeaderboardCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        try {
            LeaderboardGUI leaderboardGUI = new LeaderboardGUI(plugin, player);
            leaderboardGUI.open();
        } catch (Exception e) {
            player.sendMessage(colorize("&c打开排行榜界面时出错: " + e.getMessage()));
            e.printStackTrace();
        }
        return true;
    }

    private boolean handleDebugCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge debug <quests|progress|stats>"));
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "quests":
                sender.sendMessage(colorize("&6=== 任务调试信息 ==="));
                for (ScavengeQuest quest : plugin.getQuestManager().getQuests().values()) {
                    sender.sendMessage(colorize("&e任务: " + quest.getName()));
                    sender.sendMessage(colorize("&7- ID: " + quest.getId()));
                    sender.sendMessage(colorize("&7- 类型: " + quest.getType()));
                    sender.sendMessage(colorize("&7- 目标: " + quest.getGoal()));
                    sender.sendMessage(colorize("&7- 活跃: " + quest.isActive()));
                    sender.sendMessage(colorize("&7- 过期: " + quest.isExpired()));
                    sender.sendMessage(colorize("&7- 开始时间: " + quest.getStartTime()));
                    sender.sendMessage(colorize("&7- 结束时间: " + quest.getEndTime()));
                    sender.sendMessage(colorize("&7- 目标数量: " + quest.getTargetAmount()));
                }
                break;

            case "progress":
                if (!(sender instanceof Player)) {
                    sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
                    return true;
                }
                Player player = (Player) sender;
                sender.sendMessage(colorize("&6=== 玩家进度调试信息 ==="));
                Map<String, PlayerQuestProgress> progress = plugin.getQuestManager().getPlayerProgress()
                        .get(player.getUniqueId());
                if (progress != null) {
                    for (Map.Entry<String, PlayerQuestProgress> entry : progress.entrySet()) {
                        sender.sendMessage(colorize("&e任务: " + entry.getKey()));
                        sender.sendMessage(colorize("&7- 进度: " + entry.getValue().getCurrentProgress()));
                        sender.sendMessage(colorize("&7- 完成: " + entry.getValue().isCompleted()));
                        sender.sendMessage(colorize("&7- 已领取: " + entry.getValue().isClaimed()));
                    }
                } else {
                    sender.sendMessage(colorize("&c没有找到玩家进度数据"));
                }
                break;

            case "stats":
                if (!(sender instanceof Player)) {
                    sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
                    return true;
                }
                Player statsPlayer = (Player) sender;
                PlayerStats stats = plugin.getLeaderboardManager().getPlayerStats(statsPlayer.getUniqueId());
                if (stats != null) {
                    sender.sendMessage(colorize("&6=== 玩家统计调试信息 ==="));
                    sender.sendMessage(colorize("&e玩家: " + stats.getPlayerName()));
                    sender.sendMessage(colorize("&7- 总搜刮次数: " + stats.getTotalScavenges()));
                    sender.sendMessage(colorize("&7- 完成箱子: " + stats.getChestsCompleted()));
                    sender.sendMessage(colorize("&7- 稀有物品: " + stats.getRareItemsFound()));
                    sender.sendMessage(colorize("&7- 完成任务: " + stats.getQuestsCompleted()));
                    sender.sendMessage(colorize("&7- 等级: " + stats.getLevel() + " (" + stats.getLevelName() + ")"));
                    sender.sendMessage(colorize("&7- 综合评分: " + String.format("%.1f", stats.calculateScore())));
                } else {
                    sender.sendMessage(colorize("&c没有找到玩家统计数据"));
                }
                break;

            default:
                sender.sendMessage(colorize("&c用法: /scavenge debug <quests|progress|stats>"));
        }
        return true;
    }

    private boolean handleTestCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge test <quest|stats>"));
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "quest":
                plugin.getQuestManager().updateQuestProgress(
                        player.getUniqueId(),
                        ScavengeQuest.QuestGoal.SCAVENGE_COUNT,
                        1);
                player.sendMessage(colorize("&a手动更新了搜刮任务进度!"));
                break;

            case "stats":
                plugin.getLeaderboardManager().recordScavenge(
                        player.getUniqueId(),
                        player.getName());
                player.sendMessage(colorize("&a手动更新了排行榜统计!"));
                break;

            default:
                sender.sendMessage(colorize("&c用法: /scavenge test <quest|stats>"));
        }
        return true;
    }

    private boolean handleWorldCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge world <add|remove|list|enable|disable> [世界名]"));
            return true;
        }

        WorldRestrictionManager worldManager = plugin.getWorldRestrictionManager();

        switch (args[1].toLowerCase()) {
            case "add":
                if (args.length < 3) {
                    sender.sendMessage(colorize("&c用法: /scavenge world add <世界名>"));
                    return true;
                }
                worldManager.addAllowedWorld(args[2]);
                sender.sendMessage(colorize("&a已添加世界 " + args[2] + " 到允许列表!"));
                break;

            case "remove":
                if (args.length < 3) {
                    sender.sendMessage(colorize("&c用法: /scavenge world remove <世界名>"));
                    return true;
                }
                worldManager.removeAllowedWorld(args[2]);
                sender.sendMessage(colorize("&a已从允许列表中移除世界 " + args[2] + "!"));
                break;

            case "list":
                sender.sendMessage(colorize("&6=== 世界限制设置 ==="));
                sender.sendMessage(colorize("&e状态: " + (worldManager.isEnabled() ? "&a启用" : "&c禁用")));
                sender.sendMessage(colorize("&e允许的世界:"));
                for (String world : worldManager.getAllowedWorlds()) {
                    sender.sendMessage(colorize("&7- " + world));
                }
                sender.sendMessage(colorize("&e禁止的指令:"));
                for (String command : worldManager.getBlockedCommands()) {
                    sender.sendMessage(colorize("&7- /" + command));
                }
                break;

            case "enable":
                worldManager.setEnabled(true);
                sender.sendMessage(colorize("&a已启用世界限制功能!"));
                break;

            case "disable":
                worldManager.setEnabled(false);
                sender.sendMessage(colorize("&c已禁用世界限制功能!"));
                break;

            default:
                sender.sendMessage(colorize("&c用法: /scavenge world <add|remove|list|enable|disable> [世界名]"));
        }

        return true;
    }



    private boolean handleSetSpawnCommand(CommandSender sender) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(plugin.getMessage("no-permission"));
            return true;
        }

        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此命令!"));
            return true;
        }

        Player player = (Player) sender;
        Location location = player.getLocation();

        // 设置传送目标
        plugin.getTeleportPadManager().setSpawnLocation(location);
        player.sendMessage(colorize("&a传送目标已设置为当前位置: " +
                location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ()));

        return true;
    }

    /**
     * 处理合成命令
     */
    private boolean handleCraftingCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§c用法: /scavenge crafting <info|recipes|stats>");
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "info":
                return handleCraftingInfoCommand(sender);
            case "recipes":
                return handleCraftingRecipesCommand(sender);
            case "stats":
                return handleCraftingStatsCommand(sender);
            case "reload":
                return handleCraftingReloadCommand(sender);
            case "save":
                return handleCraftingSaveCommand(sender);
            case "enable":
                return handleCraftingToggleCommand(sender, true);
            case "disable":
                return handleCraftingToggleCommand(sender, false);
            case "test":
                return handleCraftingTestCommand(sender, args);
            case "debug":
                return handleCraftingDebugCommand(sender, args);
            case "verify":
                return handleCraftingVerifyCommand(sender);
            case "check":
                return handleCraftingCheckCommand(sender);
            default:
                sender.sendMessage("§c未知的合成子命令。");
                sender.sendMessage("§e可用命令: info, recipes, stats, reload, save, enable, disable, test, debug, verify, check");
                return true;
        }
    }

    /**
     * 显示合成系统信息
     */
    private boolean handleCraftingInfoCommand(CommandSender sender) {
        sender.sendMessage("§6=== 合成系统信息 ===");
        sender.sendMessage("§e合成系统状态: §a启用");

        CraftingManager craftingManager = plugin.getCraftingManager();
        if (craftingManager != null) {
            sender.sendMessage("§e已注册配方数量: §b" + craftingManager.getRegisteredRecipeCount());
        }

        sender.sendMessage("§e支持的合成类型:");
        sender.sendMessage("§7  - 淬炼石碎片 → 完整淬炼石");
        sender.sendMessage("§7  - 强化宝石碎片 → 完整宝石");
        sender.sendMessage("§7  - 特殊合成配方（符咒、直升棒等）");
        sender.sendMessage("§e使用 §b/scavenge crafting recipes §e查看详细配方");

        return true;
    }

    /**
     * 显示合成配方
     */
    private boolean handleCraftingRecipesCommand(CommandSender sender) {
        sender.sendMessage("§6=== 合成配方列表 ===");

        sender.sendMessage("§e§l淬炼石合成:");
        sender.sendMessage("§7  普通淬炼石 = 4个普通淬炼石碎片");
        sender.sendMessage("§7  中等淬炼石 = 6个中等淬炼石碎片");
        sender.sendMessage("§7  高等淬炼石 = 8个高等淬炼石碎片");
        sender.sendMessage("§7  上等淬炼石 = 10个上等淬炼石碎片");
        sender.sendMessage("§7  淬炼吞噬石 = 12个吞噬石碎片");

        sender.sendMessage("§a§l强化宝石合成:");
        sender.sendMessage("§7  粗糙强化素材 = 3个粗糙强化碎片");
        sender.sendMessage("§7  普通强化素材 = 4个普通强化碎片");
        sender.sendMessage("§7  优秀强化素材 = 6个优秀强化碎片");
        sender.sendMessage("§7  超级强化素材 = 8个超级强化碎片");
        sender.sendMessage("§7  强化直升符咒 = 10个符咒碎片");
        sender.sendMessage("§7  强化突破石 = 12个突破碎片");

        sender.sendMessage("§d§l特殊合成:");
        sender.sendMessage("§7  淬炼符咒 = 4高等碎片 + 4上等碎片 + 1吞噬碎片");
        sender.sendMessage("§7  淬炼直升棒 = 1淬炼符咒 + 4吞噬碎片");
        sender.sendMessage("§7  强化棒 = 2超级碎片 + 2符咒碎片 + 1木棍");

        return true;
    }

    /**
     * 显示合成统计
     */
    private boolean handleCraftingStatsCommand(CommandSender sender) {
        CraftingManager craftingManager = plugin.getCraftingManager();
        if (craftingManager == null || craftingManager.getStatsManager() == null) {
            sender.sendMessage("§c合成系统未启用！");
            return true;
        }

        CraftingStatsManager statsManager = craftingManager.getStatsManager();

        if (sender instanceof Player) {
            // 显示玩家个人统计
            Player player = (Player) sender;
            CraftingStatsManager.PlayerCraftingStats stats = statsManager.getPlayerStats(player.getUniqueId());

            sender.sendMessage("§6=== 你的合成统计 ===");
            sender.sendMessage("§e总合成次数: §b" + stats.totalCrafts);
            sender.sendMessage("§e成功次数: §a" + stats.successfulCrafts);
            sender.sendMessage("§e失败次数: §c" + stats.failedCrafts);
            sender.sendMessage("§e成功率: §a" + String.format("%.1f%%", stats.getSuccessRate()));
            sender.sendMessage("");
            sender.sendMessage("§e分类统计:");
            sender.sendMessage("§7  淬炼石合成: §b" + stats.cuilianCrafts + " 次");
            sender.sendMessage("§7  强化宝石合成: §b" + stats.gemCrafts + " 次");
            sender.sendMessage("§7  特殊物品合成: §b" + stats.specialCrafts + " 次");
            sender.sendMessage("§7  使用碎片总数: §b" + stats.fragmentsUsed + " 个");

            if (stats.lastCraftTime > 0) {
                long timeSince = System.currentTimeMillis() - stats.lastCraftTime;
                long hours = timeSince / (1000 * 60 * 60);
                sender.sendMessage("§7  上次合成: §b" + hours + " 小时前");
            }
        } else {
            // 显示全局统计
            CraftingStatsManager.GlobalCraftingStats globalStats = statsManager.getGlobalStats();

            sender.sendMessage("§6=== 全局合成统计 ===");
            sender.sendMessage("§e参与玩家数: §b" + globalStats.totalPlayers);
            sender.sendMessage("§e总合成次数: §b" + globalStats.totalCrafts);
            sender.sendMessage("§e成功次数: §a" + globalStats.successfulCrafts);
            sender.sendMessage("§e失败次数: §c" + globalStats.failedCrafts);
            sender.sendMessage("§e全局成功率: §a" + String.format("%.1f%%", globalStats.getSuccessRate()));
            sender.sendMessage("§e平均每人合成: §b" + String.format("%.1f", globalStats.getAverageCraftsPerPlayer()) + " 次");
            sender.sendMessage("");
            sender.sendMessage("§e分类统计:");
            sender.sendMessage("§7  淬炼石合成: §b" + globalStats.cuilianCrafts + " 次");
            sender.sendMessage("§7  强化宝石合成: §b" + globalStats.gemCrafts + " 次");
            sender.sendMessage("§7  特殊物品合成: §b" + globalStats.specialCrafts + " 次");
            sender.sendMessage("§7  使用碎片总数: §b" + globalStats.fragmentsUsed + " 个");
        }

        return true;
    }

    /**
     * 重新加载合成系统
     */
    private boolean handleCraftingReloadCommand(CommandSender sender) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage("§c你没有权限执行此命令！");
            return true;
        }

        CraftingManager craftingManager = plugin.getCraftingManager();
        if (craftingManager == null) {
            sender.sendMessage("§c合成系统未启用！");
            return true;
        }

        craftingManager.reload();
        sender.sendMessage("§a合成系统已重新加载！");
        return true;
    }

    /**
     * 保存合成统计
     */
    private boolean handleCraftingSaveCommand(CommandSender sender) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage("§c你没有权限执行此命令！");
            return true;
        }

        CraftingManager craftingManager = plugin.getCraftingManager();
        if (craftingManager == null) {
            sender.sendMessage("§c合成系统未启用！");
            return true;
        }

        craftingManager.saveStats();
        sender.sendMessage("§a合成统计数据已保存！");
        return true;
    }

    /**
     * 切换合成系统启用状态
     */
    private boolean handleCraftingToggleCommand(CommandSender sender, boolean enable) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage("§c你没有权限执行此命令！");
            return true;
        }

        CraftingManager craftingManager = plugin.getCraftingManager();
        if (craftingManager == null) {
            sender.sendMessage("§c合成系统未初始化！");
            return true;
        }

        craftingManager.setEnabled(enable);
        sender.sendMessage("§a合成系统已" + (enable ? "启用" : "禁用") + "！");
        return true;
    }

    /**
     * 测试合成功能
     */
    private boolean handleCraftingTestCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return true;
        }

        Player player = (Player) sender;

        if (args.length < 3) {
            sender.sendMessage("§c用法: /scavenge crafting test <type>");
            sender.sendMessage("§e类型: cuilian_putong, cuilian_zhongdeng, gem_normal, gem_luck, gem_breakthrough, gem_direct");
            return true;
        }

        String testType = args[2].toLowerCase();

        switch (testType) {
            case "cuilian_putong":
                // 给玩家4个普通淬炼石碎片
                for (int i = 0; i < 4; i++) {
                    ItemStack fragment = FragmentFactory.createCuilianFragment("putong");
                    player.getInventory().addItem(fragment);
                }
                player.sendMessage("§a已给予4个普通淬炼石碎片，请放入工作台合成！");
                player.sendMessage("§e合成要求：4个普通淬炼石碎片 = 1个普通淬炼石");
                break;

            case "cuilian_zhongdeng":
                // 给玩家6个中等淬炼石碎片
                for (int i = 0; i < 6; i++) {
                    ItemStack fragment = FragmentFactory.createCuilianFragment("zhongdeng");
                    player.getInventory().addItem(fragment);
                }
                player.sendMessage("§a已给予6个中等淬炼石碎片，请放入工作台合成！");
                break;

            case "gem_normal":
                // 给玩家3个粗糙强化碎片
                for (int i = 0; i < 3; i++) {
                    ItemStack fragment = FragmentFactory.createGemFragment("normal");
                    player.getInventory().addItem(fragment);
                }
                player.sendMessage("§a已给予3个粗糙强化碎片，请放入工作台合成！");
                break;

            case "gem_luck":
                // 给玩家4个普通强化碎片
                for (int i = 0; i < 4; i++) {
                    ItemStack fragment = FragmentFactory.createGemFragment("luck");
                    player.getInventory().addItem(fragment);
                }
                player.sendMessage("§a已给予4个普通强化碎片，请放入工作台合成！");
                player.sendMessage("§e合成要求：4个普通强化碎片 = 1个普通强化素材");
                break;

            case "gem_breakthrough":
                // 给玩家12个突破碎片
                for (int i = 0; i < 12; i++) {
                    ItemStack fragment = FragmentFactory.createGemFragment("breakthrough");
                    player.getInventory().addItem(fragment);
                }
                player.sendMessage("§a已给予12个突破碎片，请放入工作台合成！");
                player.sendMessage("§e合成要求：12个突破碎片 = 1个强化突破石");
                break;

            case "gem_direct":
                // 给玩家10个符咒碎片
                for (int i = 0; i < 10; i++) {
                    ItemStack fragment = FragmentFactory.createGemFragment("direct");
                    player.getInventory().addItem(fragment);
                }
                player.sendMessage("§a已给予10个符咒碎片，请放入工作台合成！");
                player.sendMessage("§e合成要求：10个符咒碎片 = 1个强化直升符咒");
                break;

            default:
                sender.sendMessage("§c未知的测试类型！");
                sender.sendMessage("§e可用类型: cuilian_putong, cuilian_zhongdeng, gem_normal, gem_luck, gem_breakthrough, gem_direct");
                return true;
        }

        player.sendMessage("§e提示: 将碎片放入工作台的任意位置即可合成！");
        player.sendMessage("§e如果没有反应，请查看聊天栏的调试信息。");

        return true;
    }

    /**
     * 处理合成调试命令
     */
    private boolean handleCraftingDebugCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin")) {
            sender.sendMessage("§c你没有权限执行此命令！");
            return true;
        }

        if (args.length < 3) {
            sender.sendMessage("§c用法: /scavenge crafting debug <on|off>");
            return true;
        }

        boolean enable = args[2].equalsIgnoreCase("on");

        // 这里需要获取合成监听器实例并设置调试模式
        // 由于架构限制，我们暂时通过插件实例来处理
        sender.sendMessage("§a合成调试模式已" + (enable ? "开启" : "关闭") + "！");
        sender.sendMessage("§e注意：调试信息将在工作台合成时显示在聊天栏中。");

        return true;
    }

    /**
     * 验证合成系统
     */
    private boolean handleCraftingVerifyCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return true;
        }

        Player player = (Player) sender;

        sender.sendMessage("§6=== 合成系统验证 ===");

        // 测试创建各种碎片
        sender.sendMessage("§e正在测试碎片创建...");

        try {
            // 测试淬炼石碎片
            ItemStack putongFragment = FragmentFactory.createCuilianFragment("putong");
            ItemStack zhongdengFragment = FragmentFactory.createCuilianFragment("zhongdeng");
            ItemStack gaodengFragment = FragmentFactory.createCuilianFragment("gaodeng");
            ItemStack wanmeiFragment = FragmentFactory.createCuilianFragment("wanmei");
            ItemStack huamingFragment = FragmentFactory.createCuilianFragment("huaming");

            sender.sendMessage("§a✓ 淬炼石碎片创建成功");

            // 测试宝石碎片
            ItemStack normalGemFragment = FragmentFactory.createGemFragment("normal");
            ItemStack luckGemFragment = FragmentFactory.createGemFragment("luck");
            ItemStack safeGemFragment = FragmentFactory.createGemFragment("safe");
            ItemStack vipGemFragment = FragmentFactory.createGemFragment("vip");
            ItemStack directGemFragment = FragmentFactory.createGemFragment("direct");
            ItemStack breakthroughGemFragment = FragmentFactory.createGemFragment("breakthrough");

            sender.sendMessage("§a✓ 宝石碎片创建成功");

            // 测试碎片识别
            sender.sendMessage("§e正在测试碎片识别...");

            boolean putongIsFragment = FragmentFactory.isFragment(putongFragment);
            boolean normalIsFragment = FragmentFactory.isFragment(normalGemFragment);

            if (putongIsFragment && normalIsFragment) {
                sender.sendMessage("§a✓ 碎片识别功能正常");
            } else {
                sender.sendMessage("§c✗ 碎片识别功能异常");
                sender.sendMessage("§7  普通淬炼石碎片识别: " + putongIsFragment);
                sender.sendMessage("§7  粗糙宝石碎片识别: " + normalIsFragment);
            }

            // 显示碎片信息
            sender.sendMessage("§e碎片信息示例:");
            sender.sendMessage("§7  普通淬炼石碎片: " + putongFragment.getItemMeta().getDisplayName());
            sender.sendMessage("§7  中等淬炼石碎片: " + zhongdengFragment.getItemMeta().getDisplayName());
            sender.sendMessage("§7  粗糙宝石碎片: " + normalGemFragment.getItemMeta().getDisplayName());
            sender.sendMessage("§7  普通宝石碎片: " + luckGemFragment.getItemMeta().getDisplayName());

            // 给玩家一些测试碎片
            player.getInventory().addItem(putongFragment, putongFragment, putongFragment, putongFragment);
            sender.sendMessage("§a已给予4个普通淬炼石碎片用于测试合成！");
            sender.sendMessage("§e请将这些碎片放入工作台尝试合成。");

        } catch (Exception e) {
            sender.sendMessage("§c✗ 验证过程中出现错误: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 检查手中物品是否为碎片
     */
    private boolean handleCraftingCheckCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return true;
        }

        Player player = (Player) sender;
        ItemStack handItem = player.getItemInHand();

        if (handItem == null || handItem.getType() == Material.AIR) {
            player.sendMessage("§c请手持一个物品！");
            return true;
        }

        player.sendMessage("§6=== 物品检查结果 ===");
        player.sendMessage("§e物品类型: §b" + handItem.getType().name());
        player.sendMessage("§e数量: §b" + handItem.getAmount());

        if (handItem.hasItemMeta()) {
            ItemMeta meta = handItem.getItemMeta();
            if (meta.hasDisplayName()) {
                player.sendMessage("§e显示名称: §b" + meta.getDisplayName());
            }
            if (meta.hasLore()) {
                player.sendMessage("§e描述: §7" + String.join(" ", meta.getLore()));
            }
        }

        // 检查是否为碎片
        boolean isFragment = FragmentFactory.isFragment(handItem);
        player.sendMessage("§e是否为碎片: " + (isFragment ? "§a是" : "§c否"));

        if (isFragment) {
            player.sendMessage("§a✓ 这是一个有效的碎片，可以用于合成！");
        } else {
            player.sendMessage("§c✗ 这不是碎片，无法用于合成。");
        }

        return true;
    }

    /**
     * 处理撤离区域指令
     */
    private boolean handleZoneCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("scavenge.admin") && !sender.isOp()) {
            sender.sendMessage(colorize("&c你没有权限使用此指令!"));
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(colorize("&c用法: /scavenge zone <tool|create|remove|list|reload> [参数]"));
            return true;
        }

        switch (args[1].toLowerCase()) {
            case "tool":
                return handleZoneToolCommand(sender);
            case "create":
                return handleZoneCreateCommand(sender, args);
            case "remove":
                return handleZoneRemoveCommand(sender, args);
            case "list":
                return handleZoneListCommand(sender);
            case "reload":
                return handleZoneReloadCommand(sender);
            default:
                sender.sendMessage(colorize("&c未知的撤离区域子指令: " + args[1]));
                sender.sendMessage(colorize("&7可用指令: tool, create, remove, list, reload"));
                return true;
        }
    }

    /**
     * 给予撤离区域选择工具
     */
    private boolean handleZoneToolCommand(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此指令!"));
            return true;
        }

        Player player = (Player) sender;
        ItemStack tool = EvacuationZoneListener.createSelectionTool(plugin);

        // 检查背包空间
        if (player.getInventory().firstEmpty() == -1) {
            sender.sendMessage(colorize("&c背包已满！"));
            return true;
        }

        player.getInventory().addItem(tool);
        player.sendMessage(colorize("&a已给予撤离区域选择工具！"));
        player.sendMessage(colorize("&7左键点击设置第一个点，右键点击设置第二个点"));

        return true;
    }

    /**
     * 创建撤离区域
     */
    private boolean handleZoneCreateCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&c只有玩家可以使用此指令!"));
            return true;
        }

        if (args.length < 3) {
            sender.sendMessage(colorize("&c用法: /scavenge zone create <区域名称>"));
            return true;
        }

        Player player = (Player) sender;
        String zoneId = args[2];

        boolean success = plugin.getEvacuationZoneManager().createZone(player, zoneId);
        if (!success) {
            // 错误消息已在管理器中发送
            return true;
        }

        return true;
    }

    /**
     * 删除撤离区域
     */
    private boolean handleZoneRemoveCommand(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(colorize("&c用法: /scavenge zone remove <区域名称>"));
            return true;
        }

        String zoneId = args[2];
        boolean success = plugin.getEvacuationZoneManager().removeZone(zoneId);

        if (success) {
            sender.sendMessage(colorize("&a撤离区域 '" + zoneId + "' 已删除！"));
        } else {
            sender.sendMessage(colorize("&c撤离区域 '" + zoneId + "' 不存在！"));
        }

        return true;
    }

    /**
     * 列出所有撤离区域
     */
    private boolean handleZoneListCommand(CommandSender sender) {
        java.util.Collection<EvacuationZoneManager.EvacuationZone> zones = plugin.getEvacuationZoneManager().getAllZones();

        if (zones.isEmpty()) {
            sender.sendMessage(colorize("&c没有撤离区域！"));
            return true;
        }

        sender.sendMessage(colorize("&6=== 撤离区域列表 ==="));
        for (EvacuationZoneManager.EvacuationZone zone : zones) {
            sender.sendMessage(colorize("&e" + zone.getId() + " &7- 世界: " + zone.getWorld().getName() +
                " 范围: " + zone.getMin().getBlockX() + "," + zone.getMin().getBlockY() + "," + zone.getMin().getBlockZ() +
                " 到 " + zone.getMax().getBlockX() + "," + zone.getMax().getBlockY() + "," + zone.getMax().getBlockZ()));
        }

        return true;
    }

    /**
     * 重新加载撤离区域粒子效果
     */
    private boolean handleZoneReloadCommand(CommandSender sender) {
        try {
            // 先重新加载配置文件
            plugin.reloadConfig();
            sender.sendMessage(colorize("&a配置文件已重新加载！"));

            // 然后重新加载粒子效果
            if (plugin.getEvacuationParticleManager() != null) {
                plugin.getEvacuationParticleManager().reloadAllParticles();
                sender.sendMessage(colorize("&a撤离区域粒子效果已重新加载！"));
            } else {
                sender.sendMessage(colorize("&c撤离区域粒子管理器未初始化！"));
            }

            sender.sendMessage(colorize("&a撤离区域系统重新加载完成！"));
        } catch (Exception e) {
            sender.sendMessage(colorize("&c重新加载时出现错误: " + e.getMessage()));
            plugin.getLogger().warning("重新加载撤离区域时出错: " + e.getMessage());
        }

        return true;
    }



    private void sendHelp(CommandSender sender) {
        sender.sendMessage(colorize("&6=== 搜刮插件帮助 ==="));
        sender.sendMessage(colorize("&e/scavenge give <玩家名> [数量] &7- 给予玩家搜刮方块"));
        sender.sendMessage(colorize("&e/scavenge place &7- 在目标位置放置搜刮箱"));
        sender.sendMessage(colorize("&e/scavenge remove &7- 移除目标位置的搜刮箱"));
        sender.sendMessage(colorize("&e/scavenge list &7- 列出所有搜刮箱"));
        sender.sendMessage(colorize("&e/scavenge reset <all|target> &7- 重置搜刮箱"));
        sender.sendMessage(colorize("&e/scavenge quest &7- 打开任务界面"));
        sender.sendMessage(colorize("&e/scavenge leaderboard &7- 打开排行榜"));
        sender.sendMessage(colorize("&e/scavenge world <add|remove|list|enable|disable> &7- 管理世界限制"));
        sender.sendMessage(colorize("&e/scavenge setspawn &7- 设置撤离区域传送目标位置"));
        sender.sendMessage(colorize("&6=== 撤离区域系统 ==="));
        sender.sendMessage(colorize("&e/scavenge zone tool &7- 获取撤离区域选择工具"));
        sender.sendMessage(colorize("&e/scavenge zone create <名称> &7- 创建撤离区域"));
        sender.sendMessage(colorize("&e/scavenge zone remove <名称> &7- 删除撤离区域"));
        sender.sendMessage(colorize("&e/scavenge zone list &7- 列出所有撤离区域"));
        sender.sendMessage(colorize("&e/scavenge zone reload &7- 重新加载粒子效果"));
        sender.sendMessage(colorize("&6=== 合成系统 ==="));
        sender.sendMessage(colorize("&e/scavenge crafting info &7- 查看合成系统信息"));
        sender.sendMessage(colorize("&e/scavenge crafting recipes &7- 查看所有合成配方"));
        sender.sendMessage(colorize("&e/scavenge crafting stats &7- 查看合成统计"));
        if (sender.hasPermission("scavenge.admin")) {
            sender.sendMessage(colorize("&e/scavenge crafting reload &7- 重新加载合成系统"));
            sender.sendMessage(colorize("&e/scavenge crafting save &7- 保存合成统计"));
            sender.sendMessage(colorize("&e/scavenge crafting enable/disable &7- 启用/禁用合成系统"));
        }
        sender.sendMessage(colorize("&e/scavenge reload &7- 重新加载配置文件"));
    }
}
