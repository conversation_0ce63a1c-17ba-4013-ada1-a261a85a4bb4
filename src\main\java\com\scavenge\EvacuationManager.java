package com.scavenge;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * 撤离点管理器 - 管理撤离点物品的创建和识别
 */
public class EvacuationManager {

    private final ScavengePlugin plugin;

    public EvacuationManager(ScavengePlugin plugin) {
        this.plugin = plugin;
    }

    /**
     * 创建撤离点物品
     */
    public ItemStack createEvacuationItem() {
        // 获取配置
        String materialName = plugin.getConfig().getString("teleport-pad.evacuation-item.material", "GOLD_PLATE");
        String displayName = plugin.getConfig().getString("teleport-pad.evacuation-item.display-name", "&6&l撤离点");
        List<String> lore = plugin.getConfig().getStringList("teleport-pad.evacuation-item.lore");
        boolean enchanted = plugin.getConfig().getBoolean("teleport-pad.evacuation-item.enchanted", true);

        // 创建物品
        Material material;
        try {
            material = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("无效的撤离点物品材质: " + materialName + "，使用默认材质 GOLD_PLATE");
            material = Material.GOLD_PLATE;
        }

        ItemStack item = new ItemStack(material, 1);
        ItemMeta meta = item.getItemMeta();

        // 设置显示名称
        if (meta != null) {
            meta.setDisplayName(displayName.replace("&", "§"));

            // 设置描述
            if (lore != null && !lore.isEmpty()) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(line.replace("&", "§"));
                }
                meta.setLore(coloredLore);
            }

            // 添加发光效果
            if (enchanted) {
                meta.addEnchant(Enchantment.DURABILITY, 1, true);
                meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
            }

            // 添加特殊标记用于识别
            meta.setDisplayName(meta.getDisplayName() + "§r§8§l[EVACUATION]");

            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * 检查物品是否是撤离点物品
     */
    public boolean isEvacuationItem(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return false;
        }

        String displayName = meta.getDisplayName();
        return displayName.contains("§r§8§l[EVACUATION]");
    }

    /**
     * 获取撤离点物品的显示名称（去除标记）
     */
    public String getCleanDisplayName(ItemStack item) {
        if (!isEvacuationItem(item)) {
            return null;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return null;
        }

        String displayName = meta.getDisplayName();
        return displayName.replace("§r§8§l[EVACUATION]", "");
    }
}
