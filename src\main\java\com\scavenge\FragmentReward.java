package com.scavenge;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Random;

/**
 * 碎片奖励类
 * 继承自ScavengeReward，专门处理碎片奖励
 */
public class FragmentReward extends ScavengeReward {
    
    private final String fragmentType;
    private final String subType;
    private final String materialName;
    private final int minAmount;
    private final int maxAmount;
    private final Random random;
    
    public FragmentReward(String displayName, String materialName, String fragmentType, String subType,
                         int minAmount, int maxAmount, double chance, int progressTime, boolean isRare) {
        // 调用父类构造函数，使用FLINT作为默认材质
        super(Material.FLINT, minAmount, maxAmount, displayName, null, null, chance, progressTime);
        
        this.fragmentType = fragmentType;
        this.subType = subType;
        this.materialName = materialName;
        this.minAmount = minAmount;
        this.maxAmount = maxAmount;
        this.random = new Random();
    }
    
    @Override
    public ItemStack createItemStack() {
        // 计算数量
        int amount = minAmount;
        if (maxAmount > minAmount) {
            amount = random.nextInt(maxAmount - minAmount + 1) + minAmount;
        }
        
        // 创建碎片物品
        ItemStack fragment = FragmentFactory.createFragment(fragmentType, subType);
        fragment.setAmount(amount);
        
        return fragment;
    }
    
    @Override
    public ItemStack createDisplayItem() {
        // 创建用于GUI显示的物品
        try {
            Material displayMaterial = Material.valueOf(materialName.toUpperCase());
            ItemStack displayItem = new ItemStack(displayMaterial, 1);
            
            // 设置显示名称
            if (displayItem.getItemMeta() != null) {
                displayItem.getItemMeta().setDisplayName(getDisplayName().replace("&", "§"));
                displayItem.setItemMeta(displayItem.getItemMeta());
            }
            
            return displayItem;
        } catch (IllegalArgumentException e) {
            // 如果材质无效，使用默认的FLINT
            return super.createDisplayItem();
        }
    }
    
    @Override
    public void giveReward(Player player) {
        ItemStack reward = createItemStack();
        
        // 检查背包空间
        if (player.getInventory().firstEmpty() == -1) {
            // 背包满了，掉落到地上
            player.getWorld().dropItemNaturally(player.getLocation(), reward);
            player.sendMessage("§e§l背包已满，碎片已掉落在地上！");
        } else {
            player.getInventory().addItem(reward);
        }
        
        // 发送获得消息
        String itemName = reward.getItemMeta().getDisplayName();
        if (itemName == null || itemName.isEmpty()) {
            itemName = "神秘碎片";
        }
        
        player.sendMessage("§a你获得了 " + itemName + " §ax" + reward.getAmount() + "！");
    }
    
    /**
     * 获取碎片类型
     */
    public String getFragmentType() {
        return fragmentType;
    }
    
    /**
     * 获取子类型
     */
    public String getSubType() {
        return subType;
    }
    
    /**
     * 获取显示材质名称
     */
    public String getMaterialName() {
        return materialName;
    }
}
