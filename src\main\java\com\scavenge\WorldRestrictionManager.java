package com.scavenge;

import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerCommandPreprocessEvent;

import java.util.List;

/**
 * 世界限制管理器 - 管理搜刮箱的世界限制和指令限制
 */
public class WorldRestrictionManager implements Listener {
    
    private final ScavengePlugin plugin;
    
    public WorldRestrictionManager(ScavengePlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 检查玩家是否在允许的世界中
     */
    public boolean isWorldAllowed(Player player) {
        if (!plugin.getConfig().getBoolean("world-restrictions.enabled", false)) {
            return true; // 如果未启用世界限制，允许所有世界
        }
        
        String worldName = player.getWorld().getName();
        List<String> allowedWorlds = plugin.getConfig().getStringList("world-restrictions.allowed-worlds");
        
        return allowedWorlds.contains(worldName);
    }
    
    /**
     * 检查世界是否允许使用搜刮箱
     */
    public boolean isWorldAllowed(World world) {
        if (!plugin.getConfig().getBoolean("world-restrictions.enabled", false)) {
            return true; // 如果未启用世界限制，允许所有世界
        }
        
        String worldName = world.getName();
        List<String> allowedWorlds = plugin.getConfig().getStringList("world-restrictions.allowed-worlds");
        
        return allowedWorlds.contains(worldName);
    }
    
    /**
     * 检查指令是否被禁止
     */
    public boolean isCommandBlocked(String command) {
        if (!plugin.getConfig().getBoolean("world-restrictions.enabled", false)) {
            return false; // 如果未启用世界限制，不禁止任何指令
        }
        
        List<String> blockedCommands = plugin.getConfig().getStringList("world-restrictions.blocked-commands");
        
        // 移除指令前缀 "/" 并转换为小写
        String cleanCommand = command.toLowerCase();
        if (cleanCommand.startsWith("/")) {
            cleanCommand = cleanCommand.substring(1);
        }
        
        // 检查指令是否在禁止列表中
        for (String blockedCommand : blockedCommands) {
            if (cleanCommand.startsWith(blockedCommand.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 监听玩家指令事件，阻止在搜刮世界中使用禁止的指令
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerCommand(PlayerCommandPreprocessEvent event) {
        Player player = event.getPlayer();
        String command = event.getMessage();
        
        // 检查玩家是否在允许的世界中
        if (!isWorldAllowed(player)) {
            return; // 如果不在搜刮世界中，不限制指令
        }
        
        // 检查指令是否被禁止
        if (isCommandBlocked(command)) {
            event.setCancelled(true);
            player.sendMessage(plugin.getMessage("command-blocked"));
            
            // 记录日志
            plugin.getLogger().info("阻止玩家 " + player.getName() + " 在世界 " + 
                    player.getWorld().getName() + " 中使用禁止的指令: " + command);
        }
    }
    
    /**
     * 获取允许的世界列表
     */
    public List<String> getAllowedWorlds() {
        return plugin.getConfig().getStringList("world-restrictions.allowed-worlds");
    }
    
    /**
     * 获取禁止的指令列表
     */
    public List<String> getBlockedCommands() {
        return plugin.getConfig().getStringList("world-restrictions.blocked-commands");
    }
    
    /**
     * 检查世界限制是否启用
     */
    public boolean isEnabled() {
        return plugin.getConfig().getBoolean("world-restrictions.enabled", false);
    }
    
    /**
     * 添加允许的世界
     */
    public void addAllowedWorld(String worldName) {
        List<String> allowedWorlds = getAllowedWorlds();
        if (!allowedWorlds.contains(worldName)) {
            allowedWorlds.add(worldName);
            plugin.getConfig().set("world-restrictions.allowed-worlds", allowedWorlds);
            plugin.saveConfig();
        }
    }
    
    /**
     * 移除允许的世界
     */
    public void removeAllowedWorld(String worldName) {
        List<String> allowedWorlds = getAllowedWorlds();
        if (allowedWorlds.contains(worldName)) {
            allowedWorlds.remove(worldName);
            plugin.getConfig().set("world-restrictions.allowed-worlds", allowedWorlds);
            plugin.saveConfig();
        }
    }
    
    /**
     * 添加禁止的指令
     */
    public void addBlockedCommand(String command) {
        List<String> blockedCommands = getBlockedCommands();
        if (!blockedCommands.contains(command)) {
            blockedCommands.add(command);
            plugin.getConfig().set("world-restrictions.blocked-commands", blockedCommands);
            plugin.saveConfig();
        }
    }
    
    /**
     * 移除禁止的指令
     */
    public void removeBlockedCommand(String command) {
        List<String> blockedCommands = getBlockedCommands();
        if (blockedCommands.contains(command)) {
            blockedCommands.remove(command);
            plugin.getConfig().set("world-restrictions.blocked-commands", blockedCommands);
            plugin.saveConfig();
        }
    }
    
    /**
     * 设置世界限制启用状态
     */
    public void setEnabled(boolean enabled) {
        plugin.getConfig().set("world-restrictions.enabled", enabled);
        plugin.saveConfig();
    }
}
